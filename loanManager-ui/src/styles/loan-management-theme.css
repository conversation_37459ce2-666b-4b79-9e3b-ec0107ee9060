/* Professional Loan Management System Theme - Bootstrap 5 */

/* Color Palette */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --muted-color: #6c757d;
  
  /* Loan Management Specific Colors */
  --loan-approved: #28a745;
  --loan-pending: #ffc107;
  --loan-rejected: #dc3545;
  --loan-overdue: #fd7e14;
  
  /* Professional Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Global Styles */
body {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: var(--dark-color);
}

/* Card Enhancements */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: none;
  padding: 1.25rem 1.5rem;
}

.card-header.bg-primary {
  background: var(--gradient-primary) !important;
}

.card-body {
  padding: 1.5rem;
}

/* Form Enhancements */
.form-label {
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control, .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
}

.invalid-feedback {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Button Enhancements */
.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #3d8bfd 0%, #00d4ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.btn-outline-secondary {
  border: 2px solid #dee2e6;
  color: var(--muted-color);
}

.btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: var(--dark-color);
}

/* Tag Input Styling */
tag-input {
  display: block;
}

tag-input .ng2-tag-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  min-height: 45px;
  padding: 0.5rem;
}

tag-input .ng2-tag-input.ng2-tag-input--focused {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

tag-input tag {
  background: var(--gradient-primary);
  color: white;
  border-radius: 20px;
  padding: 0.25rem 0.75rem;
  margin: 0.25rem;
  font-size: 0.875rem;
}

/* Dropzone Styling */
.custom-dropzone {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.custom-dropzone:hover {
  border-color: var(--secondary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

.custom-dropzone ngx-dropzone-label {
  color: var(--muted-color);
}

/* Form Switch Enhancement */
.form-check-input[type="checkbox"] {
  width: 3rem;
  height: 1.5rem;
  border-radius: 1rem;
}

.form-check-input:checked {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

/* Professional Status Badges */
.badge-loan-approved {
  background-color: var(--loan-approved);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.badge-loan-pending {
  background-color: var(--loan-pending);
  color: var(--dark-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.badge-loan-rejected {
  background-color: var(--loan-rejected);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

.badge-loan-overdue {
  background-color: var(--loan-overdue);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
}

/* Professional Table Styling */
.table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.table thead th {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 1rem;
  font-weight: 600;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

/* Professional Alert Styling */
.alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
}

.alert-success {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

.alert-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
  color: var(--warning-color);
  border-left: 4px solid var(--warning-color);
}

.alert-danger {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
  color: var(--danger-color);
  border-left: 4px solid var(--danger-color);
}

/* Professional Modal Styling */
.modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  border-radius: 16px 16px 0 0;
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 16px 16px;
  padding: 1.5rem;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .form-control, .form-select {
    padding: 0.5rem 0.75rem;
  }
}

/* Loading Spinner */
.spinner-border-custom {
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
  border-color: var(--secondary-color);
  border-right-color: transparent;
}

/* Professional Navigation */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--secondary-color) !important;
}

/* Professional Sidebar */
.sidebar {
  background: var(--gradient-primary);
  min-height: 100vh;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}
