import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {Loan} from "../model/loan";
import {BusinessConstants} from "../business-constants";

@Injectable({
  providedIn: 'root'
})
export class LoanService {

  constructor(private http: HttpClient) {
  }

  public save(loan: Loan) {
    return this.http.post<any>(BusinessConstants.SAVE_LOAN, loan);
  }

  public getAllLoans(page, pageSize) {
    return this.http.get<any>(BusinessConstants.GET_ALL_LOANS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  public getAllPendingLoans(page, pageSize) {
    return this.http.get<any>(BusinessConstants.GET_ALL_PENDING_LOANS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  public getAllArrearsLoans(page, pageSize) {
    return this.http.get<any>(BusinessConstants.GET_ALL_ARREARS_LOANS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  findByNic(nic) {
    return this.http.get(BusinessConstants.GET_LOAN_BY_BORROWER_NIC, {params: {nic: nic}});
  }

  findByLoanNo(loanNo) {
    return this.http.get(BusinessConstants.GET_LOAN_BY_LOAN_NO, {params: {loanNo: loanNo}});
  }

  findByStatus(status, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_LOAN_BY_STATUS, {
      params: {
        page: page,
        pageSize: pageSize,
        statusId: status
      }
    });
  }

  approveLoan(loanNo) {
    return this.http.get(BusinessConstants.APPROVE_LOAN, {params: {loanNo: loanNo}});
  }

  rejectLoan(loanNo, reason) {
    return this.http.get(BusinessConstants.REJECT_LOAN, {params: {loanNo: loanNo, reason: reason}});
  }

}
