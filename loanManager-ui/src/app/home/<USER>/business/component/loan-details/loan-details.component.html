<div class="card">
  <div class="card-header">
    <strong><PERSON>an <PERSON></strong>
  </div>
  <div class="card-body">
    <form #loanForm="ngForm">
      <div class="row">
        <div class="form-group col-md-3">
          <label><PERSON>rrower</label>
          <input [ngModel]="loan.borrower.name"
                 autocomplete="off" name="loanCustomerName" id="loanCustomerName"
                 class="form-control">
        </div>
        <div class="form-group col-md-3">
          <label>Borrower T.P</label>
          <input [ngModel]="loan.borrower.telephone1"
                 autocomplete="off" name="loanCustomerTp" id="loanCustomerTp"
                 class="form-control">
        </div>
        <div class="form-group col-md-3">
          <label>Loan Plan</label>
          <input [ngModel]="loan.loanPlan.name"
                 autocomplete="off" name="loanPlan" id="loanPlan"
                 class="form-control">
        </div>
        <div class="form-group col-md-3">
          <label>Loan Status</label>
          <input [ngModel]="loan.status.value"
                 autocomplete="off" name="loanStatus" id="loanStatus"
                 class="form-control">
        </div>
        <div class="form-group col-md-3">
          <label>Issued Date</label>
          <input type="date"
                 class="form-control" id="issueDate" [ngModel]="loan.dateTime" name="issueDate">
        </div>
        <div class="form-group col-md-3">
          <label>Loan Amount</label>
          <input type="text" class="form-control" id="loanAmount"
                 [ngModel]="loan.loanAmount | number: '1.2-2'" name="loanAmount">
        </div>
        <div class="form-group col-md-3">
          <label>Installment Amount (Rs)</label>
          <input type="text" class="form-control" id="installment"
                 [ngModel]="loan.installmentAmount | number: '1.2-2'" name="installment">
        </div>
        <div class="form-group col-md-3">
          <label>Loan Amount With Interest</label>
          <input type="text" class="form-control" id="loanAmountWithInterest"
                 [ngModel]="loan.loanAmountWithInterest | number: '1.2-2'" name="loanAmountWithInterest">
        </div>
        <div class="form-group col-md-2">
          <label>Paid Amount</label>
          <input type="text" class="form-control" id="paidAmount"
                 [ngModel]="loan.paidAmount | number: '1.2-2'" name="paidAmount">
        </div>
        <div class="form-group col-md-2">
          <label>Arrears Amount</label>
          <input type="text" class="form-control" id="arrearsAmount"
                 [ngModel]="loan.arrearsAmount | number: '1.2-2'" name="arrearsAmount">
        </div>
        <div class="form-group col-md-2">
          <label>Over paid Amount</label>
          <input type="text" class="form-control" id="overPaidAmount"
                 [ngModel]="loan.overPaidAmount | number: '1.2-2'" name="overPaidAmount">
        </div>
        <div class="form-group col-md-2">
          <label>Settlement Date</label>
          <input type="text" class="form-control" id="settlementDate"
                 [ngModel]="loan.settlementDate" name="settlementDate">
        </div>
        <div class="form-group col-md-2">
          <label>No of Installment Left</label>
          <input type="text" class="form-control" id="installmentLeft"
                 [ngModel]="loan.installmentLeft" name="installmentLeft">
        </div>
        <div class="form-group col-md-2">
          <label>Balance</label>
          <input type="text"  class="form-control" id="balance"
                 name="balance" [ngModel]="loan.balance">
        </div>
      </div>
      <div class="row">
        <div class="table-custom col-md-12">
          <table class="table table-bordered table-sm">
            <thead>
            <tr class="text-center">
              <th scope="col">Date</th>
              <th scope="col">Installment</th>
              <th scope="col">Payment</th>
              <th scope="col">Balance</th>
              <th scope="col">Over paid Share</th>
              <th scope="col">Date Due</th>
              <th scope="col">Status</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let rec of records,let i = index"
                class="text-center"
                [ngClass]="{'table-warning': rec.status.name === '1', 'table-success': rec.status.name === '3',
          'table-danger': rec.status.name === '2'}">
              <td>{{rec.installmentDate}}</td>
              <td>{{rec.installmentAmount | number: '1.2-2'}}</td>
              <td>{{rec.paidAmount | number: '1.2-2'}}</td>
              <td>{{rec.balance | number: '1.2-2'}}</td>
              <td>{{rec.paidAmountByOverPaid | number: '1.2-2'}}</td>
              <td>{{rec.daysDue}}</td>
              <td>{{rec.status.value}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <label>Record Count : {{recCount}}##</label>
      <label class="ml-2">Paid Amount : {{paidAmount}}##</label>
      <label class="ml-2">Installment Total : {{installmentTotal}}##</label>
      <label class="ml-2">Over Paid Total: {{overPaidAmount}}###</label>
      <label class="ml-2">Total Settled by O.P : {{overPaidSettledAmount}}</label>
    </form>
  </div>
</div>
