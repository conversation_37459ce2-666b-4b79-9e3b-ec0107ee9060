import {Component, OnInit} from '@angular/core';
import {Loan} from "../../model/loan";
import {NgForm} from "@angular/forms";
import {<PERSON>rrower} from "../../../borrower/model/borrower";
import {BorrowerService} from "../../../borrower/service/borrower.service";
import {LoanPlan} from "../../model/loanPlan";
import {LoanPlanService} from "../../service/loanPlanService";
import {LoanService} from "../../service/loanService";
import {NotificationService} from "../../../../core/service/notification.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {NewBorrowerComponent} from "../../../borrower/component/new-borrower/new-borrower.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";

@Component({
  standalone: false,
  selector: 'app-create-loan',
  templateUrl: './create-loan.component.html',
  styleUrls: ['./create-loan.component.css']
})
export class CreateLoanComponent implements OnInit {

  loan: Loan;
  isEdit: boolean;
  customers: Array<Borrower> = [];
  searchKey: string;
  loanPlans: Array<LoanPlan> = [];
  statusList: Array<MetaData> = [];

  customerModal: BsModalRef;

  isSubmitting = false;

  constructor(private customerService: BorrowerService, private loanPlanService: LoanPlanService,
              private loanService: LoanService, private notificationService: NotificationService,
              private modalService: BsModalService,private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.isSubmitting = false;
    this.loan = new Loan();
    this.getAllLoanPlans();
    this.findAllStatus();
  }

  clear() {
    this.isSubmitting = true;
    this.loan = new Loan();
  }

  loadCustomer() {
    if (this.searchKey !== '') {
      this.customerService.findByNameLike(this.searchKey).subscribe((data: Array<Borrower>) => {
        this.customers = data;
      });
    } else {
      this.ngOnInit();
    }
  }

  setSelectedCustomer(event) {
    this.loan.borrower = event.item;
  }

  saveLoan(loanForm: NgForm) {
    this.isSubmitting = true;
    if (null == this.loan.borrower || null == this.loan.loanPlan) {
      this.notificationService.showWarning("Please add necessary data");
      return;
    }
    this.loanService.save(this.loan).subscribe(result => {
      if (result.code === 200) {
        this.notificationService.showSuccess("Loan Created");
        this.ngOnInit();
        this.isSubmitting = false;
      } else {
        this.notificationService.showWarning("Creating Loan Failed");
        this.isSubmitting = false;
      }
    });
  }

  getAllLoanPlans() {
    this.loanPlanService.findAllLoanPlan().subscribe((data: Array<LoanPlan>) => {
      this.loanPlans = data;
    })
  }

  setInstallment() {
    if (null != this.loan.loanPlan && this.loan.loanAmount) {
      this.loan.installmentAmount = this.loan.loanAmount / this.loan.loanPlan.totalNoOfInstallments;
    }
  }

  openCustomerModal() {
    this.customerModal = this.modalService.show(NewBorrowerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalService.onHide.subscribe(event => {
      this.loan.borrower.id = this.customerModal.content.customer.id;
    })
  }

  findAllStatus() {
    this.metaDataService.findByCategory("Loan Status").subscribe((data: any) => {
      this.statusList = data;
    });
  }

}
