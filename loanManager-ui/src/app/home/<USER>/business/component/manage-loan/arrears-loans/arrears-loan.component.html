<div class="arrears-loans-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-exclamation-triangle"></i>
        Arrears Loans
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="loadLoans()">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
      </div>
    </div>
    <p class="page-description">
      Manage loans that are overdue and require immediate attention.
    </p>
  </div>

  <div class="content-card">
    <!-- Search Section -->
    <div class="search-section">
      <div class="form-group input-group input-focus">
        <div class="input-group-prepend">
          <span class="input-group-text bg-white"><i class="fa fa-filter"></i></span>
        </div>
        <input [(ngModel)]="searchKey"
               [typeahead]="loans"
               (typeaheadLoading)="loadLoans()"
               (typeaheadOnSelect)="setSelectedLoans($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               autocomplete="off"
               placeholder="Search By NIC"
               class="form-control" name=loans>
      </div>
    </div>

    <!-- Loans Table -->
    <div class="table-section">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Borrower Name</th>
              <th>Loan Plan</th>
              <th>Loan Amount</th>
              <th>Loan Balance</th>
              <th>Status</th>
              <th>Loan Date</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let loan of loans,let i = index"
                (click)="selectRow(loan,i)"
                [class.table-active]="i === selectedRow"
                class="clickable-row">
              <td>
                <strong>{{loan.borrower?.name || 'N/A'}}</strong>
              </td>
              <td>{{loan.loanPlan?.name || 'N/A'}}</td>
              <td>{{loan.loanAmount | number:'1.2-2'}}</td>
              <td>{{loan.balance | number:'1.2-2'}}</td>
              <td>
                <span class="badge badge-danger">{{loan.status?.value || 'N/A'}}</span>
              </td>
              <td>{{loan.dateTime | date:'mediumDate'}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-section">
      <pagination class="pagination-sm justify-content-center"
                  [totalItems]="collectionSize"
                  [(ngModel)]="page"
                  [maxSize]="15" [itemsPerPage]="pageSize"
                  [boundaryLinks]="true"
                  (pageChanged)="pageChanged($event)">
      </pagination>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
      <div class="text-right">
        <button class="btn btn-primary" type="button" (click)="loansDetail()"
                [disabled]="selectedRow===null">
          <i class="fas fa-info-circle"></i>
          Loan Details
        </button>
      </div>
    </div>
  </div>
</div>

