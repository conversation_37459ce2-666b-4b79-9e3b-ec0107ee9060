import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Loan} from "../../../model/loan";
import {LoanService} from "../../../service/loanService";
import {NotificationService} from "../../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './pending-loan.component.html',
  styleUrls: ['./pending-loan.component.css']
})
export class PendingLoanComponent implements OnInit {

  modalRef: BsModalRef;
  searchKey: string;
  loans: Array<Loan> = [];
  selectedLoan: Loan;

  selectedRow: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private notificationService: NotificationService,
              private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.page = 0;
    this.pageSize = 10;
    this.searchKey = "";
    this.loadLoans();
  }

  loadLoans() {
    this.loanService.getAllPendingLoans(this.page, this.pageSize).subscribe((data: any) => {
      this.loans = data.content;
    });
  }

  selectRow(loan: Loan, i: number) {
    this.selectedLoan = loan;
    this.selectedRow = i;
  }

  setSelectedLoans(event) {
    this.loans = [event.item];
  }

  approveLoan() {
    if (this.selectedLoan !== null) {
      this.loanService.approveLoan(this.selectedLoan.loanNo).subscribe((data: any) => {
        if (data == true) {
          this.notificationService.showSuccess("Approved");
          this.loadLoans();
        } else {
          this.notificationService.showError("Failed");
        }
      });
    }
  }

  rejectLoan() {
    if (this.selectedLoan !== null) {
      this.loanService.rejectLoan(this.selectedLoan.loanNo, "Rejected By Admin").subscribe((data: any) => {
        if (data == true) {
          this.notificationService.showSuccess("Approved");
          this.loadLoans();
        } else {
          this.notificationService.showError("Failed");
        }
      });
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadLoans();
  }

}
