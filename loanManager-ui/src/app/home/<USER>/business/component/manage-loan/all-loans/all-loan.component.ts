import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Loan} from "../../../model/loan";
import {LoanService} from "../../../service/loanService";
import {LoanDetailsComponent} from "../../loan-details/loan-details.component";
import {BorrowerService} from "../../../../borrower/service/borrower.service";
import {<PERSON>rrow<PERSON>} from "../../../../borrower/model/borrower";
import {MetaData} from "../../../../../core/model/metaData";
import {MetaDataService} from "../../../../../core/service/metaData.service";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './all-loan.component.html',
  styleUrls: ['./all-loan.component.css']
})
export class AllLoanComponent implements OnInit {

  modalRef: BsModalRef;
  loans: Array<Loan> = [];
  borrowers: Array<Borrower> = [];
  selectedLoan: Loan;

  statusList: Array<MetaData> = [];
  selectedStatus: MetaData = new MetaData();

  selectedRow: number;

  keyNic: string;
  keyName: string;
  keyTp: string;
  keyLoanNo: string;

  loanCount: number;
  totalAmount: number;
  totalCollectable: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private modalService: BsModalService,
              private borrowerService: BorrowerService, private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 10;
    this.loadLoans();
    this.findAllStatus();
  }

  loadLoans() {
    this.loans = [];
    this.loanService.getAllLoans(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.loans = data.content;
      this.collectionSize = data.totalElements;
      this.calculateTotal();
    });
  }

  findAllStatus() {
    this.metaDataService.findByCategory("Loan Status").subscribe((data: any) => {
      this.statusList = data;
    });
  }

  filterByStatus() {
    this.loanService.findByStatus(this.selectedStatus.id, this.page - 1, this.pageSize).subscribe((data: any) => {
      this.loans = [];
      this.loans = data.content;
      this.collectionSize = data.totalElements;
      this.calculateTotal();
    });
  }

  findByNic() {
    this.borrowerService.findByNic(this.keyNic).subscribe((data: any) => {
      this.borrowers = data;
    });
  }

  findByName() {
    this.borrowerService.findByNameLike(this.keyName).subscribe((data: any) => {
      this.borrowers = data;
    });
  }

  findByTp() {
    this.borrowerService.findByTpLike(this.keyTp).subscribe((data: any) => {
      this.borrowers = data;
    });
  }

  findByLoanNo() {
    this.loans = [];
    this.loanService.findByLoanNo(this.keyLoanNo).subscribe((data: Loan) => {
      this.loans = [];
      this.loans.push(data);
    });
  }

  selectRow(loan: Loan, i: number) {
    this.selectedLoan = loan;
    this.selectedRow = i;
  }

  setSelectedLoans(event) {
    this.loanService.findByNic(event.item.nic).subscribe((data: Array<Loan>) => {
      this.loans = [];
      this.loans = data;
    });
  }

  loansDetail() {
    if (this.selectedLoan !== null) {
      this.modalRef = this.modalService.show(LoanDetailsComponent, <ModalOptions>{class: 'modal-xl'});
      this.modalRef.content.loan = this.selectedLoan;
      this.modalRef.content.findLoanRecord(this.selectedLoan.loanNo);
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadLoans();
  }

  calculateTotal() {
    this.loanCount = 0;
    this.totalAmount = 0;
    this.totalCollectable = 0;

    for (let ln of this.loans) {
      this.loanCount = this.loanCount + 1;
      this.totalAmount = this.totalAmount + ln.loanAmount;
      this.totalCollectable = this.totalCollectable + ln.loanAmountWithInterest;
    }
  }

}
