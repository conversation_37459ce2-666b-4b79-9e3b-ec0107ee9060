import { Component, OnInit } from '@angular/core';
import { HolidayService } from '../../service/holiday.service';
import { NotificationService } from '../../../../core/service/notification.service';

export interface Holiday {
  id?: string;
  name: string;
  date: string;
  description?: string;
  isRecurring: boolean;
  active: boolean;
  createdDate?: string;
  modifiedDate?: string;
}

@Component({
  standalone: false,
  selector: 'app-holiday-management',
  templateUrl: './holiday-management.component.html',
  styleUrls: ['./holiday-management.component.css']
})
export class HolidayManagementComponent implements OnInit {

  holidays: Holiday[] = [];
  holiday: Holiday = {
    name: '',
    date: '',
    description: '',
    isRecurring: false,
    active: true
  };

  selectedHoliday: Holiday | null = null;
  isEditing: boolean = false;
  loading: boolean = false;
  searchTerm: string = '';
  currentYear: number = new Date().getFullYear();

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;

  constructor(
    private holidayService: HolidayService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadHolidays();
  }

  loadHolidays(): void {
    this.loading = true;
    this.holidayService.findAll(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        if (data) {
          this.holidays = data.content || data;
          this.collectionSize = data.totalElements || this.holidays.length;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading holidays:', error);
        this.notificationService.showError('Failed to load holidays');
        this.loading = false;
      }
    });
  }

  saveHoliday(): void {
    if (!this.holiday.name || !this.holiday.date) {
      this.notificationService.showError('Please fill in all required fields');
      return;
    }

    this.loading = true;

    const saveObservable = this.isEditing
      ? this.holidayService.update(this.holiday)
      : this.holidayService.save(this.holiday);

    saveObservable.subscribe({
      next: (response) => {
        if (response) {
          this.notificationService.showSuccess(
            this.isEditing ? 'Holiday updated successfully' : 'Holiday created successfully'
          );
          this.resetForm();
          this.loadHolidays();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error saving holiday:', error);
        this.notificationService.showError('Failed to save holiday');
        this.loading = false;
      }
    });
  }

  editHoliday(holiday: Holiday): void {
    this.holiday = { ...holiday };
    this.selectedHoliday = holiday;
    this.isEditing = true;
  }

  deleteHoliday(holiday: Holiday): void {
    if (confirm('Are you sure you want to delete this holiday?')) {
      this.loading = true;
      this.holidayService.delete(holiday.id!).subscribe({
        next: (response) => {
          if (response) {
            this.notificationService.showSuccess('Holiday deleted successfully');
            this.loadHolidays();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error deleting holiday:', error);
          this.notificationService.showError('Failed to delete holiday');
          this.loading = false;
        }
      });
    }
  }

  toggleHolidayStatus(holiday: Holiday): void {
    const updatedHoliday = { ...holiday, active: !holiday.active };
    this.holidayService.update(updatedHoliday).subscribe({
      next: (response) => {
        if (response) {
          holiday.active = !holiday.active;
          this.notificationService.showSuccess(
            `Holiday ${holiday.active ? 'activated' : 'deactivated'} successfully`
          );
        }
      },
      error: (error) => {
        console.error('Error updating holiday status:', error);
        this.notificationService.showError('Failed to update holiday status');
      }
    });
  }

  resetForm(): void {
    this.holiday = {
      name: '',
      date: '',
      description: '',
      isRecurring: false,
      active: true
    };
    this.selectedHoliday = null;
    this.isEditing = false;
  }

  searchHolidays(): void {
    if (this.searchTerm.trim()) {
      this.holidayService.findByName(this.searchTerm).subscribe({
        next: (data: Holiday[]) => {
          this.holidays = data || [];
          this.collectionSize = this.holidays.length;
        },
        error: (error) => {
          console.error('Error searching holidays:', error);
          this.notificationService.showError('Failed to search holidays');
        }
      });
    } else {
      this.loadHolidays();
    }
  }

  filterByYear(year: number): void {
    this.currentYear = year;
    this.holidayService.findByYear(year).subscribe({
      next: (data: Holiday[]) => {
        this.holidays = data || [];
        this.collectionSize = this.holidays.length;
      },
      error: (error) => {
        console.error('Error filtering holidays by year:', error);
        this.notificationService.showError('Failed to filter holidays');
      }
    });
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadHolidays();
  }

  getYearOptions(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 2; i++) {
      years.push(i);
    }
    return years;
  }

  isHolidayToday(holiday: Holiday): boolean {
    const today = new Date().toISOString().split('T')[0];
    return holiday.date === today;
  }

  isHolidayUpcoming(holiday: Holiday): boolean {
    const today = new Date();
    const holidayDate = new Date(holiday.date);
    const diffTime = holidayDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 && diffDays <= 7;
  }

  addPredefinedHolidays(): void {
    const predefinedHolidays = [
      { name: 'New Year\'s Day', date: `${this.currentYear}-01-01`, isRecurring: true },
      { name: 'Christmas Day', date: `${this.currentYear}-12-25`, isRecurring: true },
      { name: 'Good Friday', date: this.getGoodFridayDate(this.currentYear), isRecurring: true }
    ];

    predefinedHolidays.forEach(holiday => {
      const newHoliday: Holiday = {
        ...holiday,
        description: 'Public Holiday',
        active: true
      };

      this.holidayService.save(newHoliday).subscribe({
        next: () => {
          this.notificationService.showSuccess(`Added ${holiday.name}`);
        },
        error: () => {
          this.notificationService.showError(`Failed to add ${holiday.name}`);
        }
      });
    });

    setTimeout(() => this.loadHolidays(), 1000);
  }

  private getGoodFridayDate(year: number): string {
    // Simple calculation for Good Friday (2 days before Easter Sunday)
    // This is a simplified version - you might want to use a proper Easter calculation
    const easter = this.getEasterDate(year);
    const goodFriday = new Date(easter);
    goodFriday.setDate(easter.getDate() - 2);
    return goodFriday.toISOString().split('T')[0];
  }

  private getEasterDate(year: number): Date {
    // Simplified Easter calculation
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;
    return new Date(year, month - 1, day);
  }
}
