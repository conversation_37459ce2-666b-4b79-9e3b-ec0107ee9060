/* Dashboard Component Specific Styles */

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Metric Cards */
.bg-gradient {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
}

.bg-success.bg-gradient {
  background: linear-gradient(135deg, var(--bs-success) 0%, #1e7e34 100%);
}

.bg-warning.bg-gradient {
  background: linear-gradient(135deg, var(--bs-warning) 0%, #d39e00 100%);
}

.bg-danger.bg-gradient {
  background: linear-gradient(135deg, var(--bs-danger) 0%, #bd2130 100%);
}

/* Timeline Styles */
.timeline {
  position: relative;
}

.timeline-item {
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 15px;
  top: 40px;
  width: 2px;
  height: calc(100% - 20px);
  background-color: #e9ecef;
}

/* Progress Bars */
.progress {
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  border-radius: 10px;
}

/* Quick Action Buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .h3 {
    font-size: 1.5rem;
  }
  
  .fa-2x {
    font-size: 1.5em;
  }
}

/* Animation for metric cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Professional shadows */
.shadow-sm {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

/* Custom button hover effects */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Professional color scheme */
:root {
  --dashboard-primary: #2c3e50;
  --dashboard-secondary: #3498db;
  --dashboard-success: #27ae60;
  --dashboard-warning: #f39c12;
  --dashboard-danger: #e74c3c;
}

/* Custom badge styles for loan status */
.badge-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.875rem;
}

.badge-approved {
  background-color: var(--dashboard-success);
  color: white;
}

.badge-pending {
  background-color: var(--dashboard-warning);
  color: white;
}

.badge-rejected {
  background-color: var(--dashboard-danger);
  color: white;
}

/* Professional table styling */
.table-dashboard {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.table-dashboard thead th {
  background: linear-gradient(135deg, var(--dashboard-primary) 0%, #34495e 100%);
  color: white;
  border: none;
  padding: 1rem;
  font-weight: 600;
}

.table-dashboard tbody tr {
  transition: all 0.3s ease;
}

.table-dashboard tbody tr:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
