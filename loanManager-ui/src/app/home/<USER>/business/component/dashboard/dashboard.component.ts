import { Component, OnInit } from '@angular/core';

@Component({
  standalone: false,
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {

  // Dashboard metrics
  totalLoans: number = 1245000;
  activeLoans: number = 156;
  pendingApprovals: number = 23;
  overdueLoans: number = 8;

  // Chart data
  loanStatusData = {
    approved: 68,
    pending: 22,
    rejected: 10
  };

  // Recent activities
  recentActivities = [
    {
      type: 'approval',
      title: '<PERSON>an Approved',
      description: 'Loan #LN001234 for ₹50,000 approved',
      time: '2 hours ago',
      icon: 'fas fa-check',
      color: 'success'
    },
    {
      type: 'borrower',
      title: 'New Borrower',
      description: '<PERSON> registered as new borrower',
      time: '4 hours ago',
      icon: 'fas fa-user',
      color: 'primary'
    },
    {
      type: 'reminder',
      title: 'Payment Reminder',
      description: '5 borrowers have payments due today',
      time: '6 hours ago',
      icon: 'fas fa-clock',
      color: 'warning'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Load dashboard metrics from service
    // This would typically call a service to get real data
    console.log('Loading dashboard data...');
  }

  createNewLoan(): void {
    // Navigate to create loan page
    console.log('Navigate to create new loan');
  }

  addBorrower(): void {
    // Navigate to add borrower page
    console.log('Navigate to add borrower');
  }

  processPayment(): void {
    // Navigate to payment processing page
    console.log('Navigate to process payment');
  }

  generateReport(): void {
    // Navigate to report generation page
    console.log('Navigate to generate report');
  }

  exportReport(): void {
    // Export dashboard report
    console.log('Exporting dashboard report...');
  }

  viewAllActivity(): void {
    // Navigate to full activity log
    console.log('Navigate to full activity log');
  }
}
