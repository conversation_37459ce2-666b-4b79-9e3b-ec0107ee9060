<div class="ledger-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-book"></i>
        Ledger Management
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="refreshData()" [disabled]="loading">
          <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
          Refresh
        </button>
        <button class="btn btn-success btn-sm" (click)="toggleAddForm()">
          <i class="fas fa-plus"></i>
          Add Record
        </button>
        <button class="btn btn-info btn-sm" (click)="exportRecords()">
          <i class="fas fa-download"></i>
          Export
        </button>
      </div>
    </div>
    <p class="page-description">
      Track and manage financial transactions, expenses, and cash flow.
    </p>
  </div>

  <!-- Ledger Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-balance">
        <div class="summary-icon">
          <i class="fas fa-wallet"></i>
        </div>
        <div class="summary-content">
          <h4>{{ledger.currentBalance | number:'1.2-2'}}</h4>
          <p>Current Balance</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-income">
        <div class="summary-icon">
          <i class="fas fa-arrow-up"></i>
        </div>
        <div class="summary-content">
          <h4>{{getTotalIncome() | number:'1.2-2'}}</h4>
          <p>Total Income</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-expense">
        <div class="summary-icon">
          <i class="fas fa-arrow-down"></i>
        </div>
        <div class="summary-content">
          <h4>{{getTotalExpense() | number:'1.2-2'}}</h4>
          <p>Total Expenses</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-net">
        <div class="summary-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="summary-content">
          <h4 [class.text-success]="getNetAmount() >= 0" [class.text-danger]="getNetAmount() < 0">
            {{getNetAmount() | number:'1.2-2'}}
          </h4>
          <p>Net Amount</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Record Form -->
  <div *ngIf="showAddForm" class="form-card mb-4">
    <div class="form-header">
      <h5>
        <i class="fas fa-plus"></i>
        Add New Ledger Record
      </h5>
      <button class="btn btn-sm btn-outline-secondary" (click)="toggleAddForm()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="form-body">
      <form (ngSubmit)="addLedgerRecord()" #recordForm="ngForm">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="recordType">Type *</label>
              <select
                id="recordType"
                class="form-control"
                [(ngModel)]="newRecord.type"
                name="recordType"
                required>
                <option value="">Select Type</option>
                <option *ngFor="let type of types" [value]="type">{{type}}</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="transactionType">Transaction Type *</label>
              <select
                id="transactionType"
                class="form-control"
                [(ngModel)]="newRecord.transactionType"
                name="transactionType"
                required>
                <option value="income">Income</option>
                <option value="expense">Expense</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="recordAmount">Amount *</label>
              <input
                type="number"
                id="recordAmount"
                class="form-control"
                [(ngModel)]="newRecord.amount"
                name="recordAmount"
                placeholder="0.00"
                min="0"
                step="0.01"
                required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="recordDate">Date *</label>
              <input
                type="date"
                id="recordDate"
                class="form-control"
                [(ngModel)]="newRecord.date"
                name="recordDate"
                required>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-9">
            <div class="form-group">
              <label for="recordDescription">Description *</label>
              <input
                type="text"
                id="recordDescription"
                class="form-control"
                [(ngModel)]="newRecord.description"
                name="recordDescription"
                placeholder="Enter description"
                required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>&nbsp;</label>
              <button
                type="submit"
                class="btn btn-primary btn-block"
                [disabled]="!recordForm.form.valid || loading">
                <i class="fas fa-save"></i>
                Save Record
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-card mb-4">
    <div class="filters-header">
      <h6>
        <i class="fas fa-filter"></i>
        Filter Records
      </h6>
    </div>
    <div class="filters-body">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label for="filterType">Type</label>
            <select
              id="filterType"
              class="form-control form-control-sm"
              [(ngModel)]="selectedType"
              (change)="filterByTypeAndDate()">
              <option value="">All Types</option>
              <option *ngFor="let type of types" [value]="type">{{type}}</option>
            </select>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="dateFrom">Date From</label>
            <input
              type="date"
              id="dateFrom"
              class="form-control form-control-sm"
              [(ngModel)]="ledgerDateFrom"
              (change)="filterByTypeAndDate()">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="dateTo">Date To</label>
            <input
              type="date"
              id="dateTo"
              class="form-control form-control-sm"
              [(ngModel)]="ledgerDateTo"
              (change)="filterByTypeAndDate()">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>&nbsp;</label>
            <button class="btn btn-outline-secondary btn-sm btn-block" (click)="clearFilters()">
              <i class="fas fa-times"></i>
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Records Table -->
  <div class="records-card">
    <div class="records-header">
      <h5>
        <i class="fas fa-list"></i>
        Ledger Records
      </h5>
      <div class="records-info">
        <span class="badge badge-primary">{{records.length}} records</span>
      </div>
    </div>
    <div class="records-body">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-2">Loading records...</p>
      </div>

      <!-- Records Table -->
      <div *ngIf="!loading && records.length > 0" class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Description</th>
              <th>Transaction</th>
              <th>Amount</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let record of records">
              <td>{{record.date | date:'mediumDate'}}</td>
              <td>
                <span class="badge badge-secondary">{{record.type}}</span>
              </td>
              <td>{{record.description}}</td>
              <td>
                <span class="badge"
                      [ngClass]="record.transactionType === 'income' ? 'badge-success' : 'badge-danger'">
                  <i class="fas" [ngClass]="record.transactionType === 'income' ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
                  {{record.transactionType | titlecase}}
                </span>
              </td>
              <td>
                <span [class.text-success]="record.transactionType === 'income'"
                      [class.text-danger]="record.transactionType === 'expense'">
                  {{record.amount | number:'1.2-2'}}
                </span>
              </td>
              <td>
                <button
                  class="btn btn-outline-danger btn-sm"
                  (click)="deleteLedgerRecord(record)"
                  title="Delete">
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="!loading && records.length === 0" class="no-data-container">
        <div class="no-data-content">
          <i class="fas fa-book-open"></i>
          <h5>No Records Found</h5>
          <p>No ledger records match your current filters. Try adjusting the filters or add a new record.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Today's Records Summary -->
  <div *ngIf="todayRecords.length > 0" class="today-records-card mt-4">
    <div class="today-header">
      <h6>
        <i class="fas fa-calendar-day"></i>
        Today's Transactions
      </h6>
    </div>
    <div class="today-body">
      <div class="row">
        <div class="col-md-6" *ngFor="let record of todayRecords">
          <div class="today-record-item">
            <div class="record-info">
              <span class="record-type">{{record.type}}</span>
              <span class="record-description">{{record.description}}</span>
            </div>
            <div class="record-amount"
                 [class.text-success]="record.transactionType === 'income'"
                 [class.text-danger]="record.transactionType === 'expense'">
              {{record.amount | number:'1.2-2'}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
