import {Component, OnInit} from '@angular/core';
import {LoanPaymentService} from "../../service/loanPaymentService";
import {LoanService} from "../../service/loanService";
import {NotificationService} from "../../../../core/service/notification.service";
import {LoanRecordService} from "../../service/loanRecord.service";
import {Loan} from "../../model/loan";
import {<PERSON>rrow<PERSON>} from "../../../borrower/model/borrower";
import {LoanRecord} from "../../model/loanRecord";

@Component({
  standalone: false,
  selector: 'app-loan-payment',
  templateUrl: './loan-payment.component.html',
  styleUrls: ['./loan-payment.component.css']
})
export class LoanPaymentComponent implements OnInit {

  records: Array<LoanRecord> = [];

  keyNic: string;
  keyLoanNo: string;
  keyTp1: string;

  borrowers: Array<Borrower> = [];

  selectedRecord: LoanRecord;
  selectedRow: number;

  payment: number;

  constructor(private loanService: LoanService, private notificationService: NotificationService,
              private loanRecordService: LoanRecordService) {
  }

  ngOnInit(): void {

  }

  search() {
    if (this.keyNic.length > 0) {
      this.loanRecordService.findPendingRecordsByNic(this.keyNic).subscribe((data: any) => {
        this.records = data;
      });
    }
    if (this.keyLoanNo.length > 0) {
      this.loanRecordService.findPendingRecordsByLoanNo(this.keyLoanNo).subscribe((data: any) => {
        this.borrowers = data;
      });
    }
    if (this.keyTp1.length > 0) {
      this.loanRecordService.findPendingRecordsByTp1(this.keyTp1).subscribe((data: any) => {
        this.borrowers = data;
      });
    }
  }

  clearSearch(val) {
    if (val === 1) {
      this.keyTp1 = "";
      this.keyLoanNo = "";
    }
    if (val === 2) {
      this.keyTp1 = "";
      this.keyNic = "";
    }
    if (val === 3) {
      this.keyNic = "";
      this.keyLoanNo = "";
    }
  }

  selectRow(rec: LoanRecord, i: number) {
    this.selectedRecord = rec;
    this.selectedRow = i;
  }

  pay() {
    if (this.payment > 0) {
      this.loanRecordService.pay(this.selectedRecord.id, this.payment).subscribe((data: any) => {
        if (data.code == 200) {
          this.notificationService.showSuccess("Payment Successful");
          this.payment = 0;
          this.search();
        } else {
          this.notificationService.showError("Payment Error");
        }
      });
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'overdue':
        return 'badge-danger';
      default:
        return 'badge-secondary';
    }
  }

}
