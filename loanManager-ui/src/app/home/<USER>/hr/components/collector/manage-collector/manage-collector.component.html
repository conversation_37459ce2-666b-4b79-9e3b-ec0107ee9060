<div class="modern-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-users-cog"></i>
        Manage Collectors
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
        <button class="btn btn-success btn-sm" routerLink="../new_collector">
          <i class="fas fa-user-plus"></i>
          New Collector
        </button>
      </div>
    </div>
    <p class="page-description">
      Search, view, and manage all loan collectors in the system.
    </p>
  </div>

  <!-- Search Card -->
  <div class="search-card">
    <div class="search-header">
      <h6>
        <i class="fas fa-search"></i>
        Search Collectors
      </h6>
    </div>
    <div class="search-body">
      <div class="row">
        <div class="input-group col-md-4">
          <input [(ngModel)]="keyNicNumberSearch"
                 [typeahead]="CollectorSearchList"
                 (typeaheadLoading)="loadNicNumber()"
                 (typeaheadOnSelect)="setSelectedNicNumber($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="nicNumber"
                 placeholder="Search NIC Number"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control"
                 name="collector">
        </div>

        <div class="input-group col-md-4">
          <input [(ngModel)]="keyCollectorSearch"
                 [typeahead]="CollectorSearchList"
                 (typeaheadLoading)="loadCollectors()"
                 (typeaheadOnSelect)="setSelectedCollector($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Search By Name"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control"
                 name="collector">
        </div>
        <div class="col-md-2 form-check text-right pt-3">
          <input class="form-check-input"
                 id="check1"
                 name="check1"
                 type="checkbox"
                 value=""
                 [(ngModel)]="inActiveFilter"
                 (change)="findCollectors()">
          <label class="form-check-label" for="check1">Inactive collectors</label>
        </div>
        <div class="col-md-2 text-right">
          <button type="button" class="btn btn-info" (click)="showAll()">View All</button>
        </div>
      </div>

      <div class="row mt-4">
        <table class="table table-striped table-bordered">
          <thead align="center">
          <tr>
            <th>NIC Number</th>
            <th>Name</th>
            <th>Address</th>
            <th>Contact No</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let collector of CollectorSearchList,let i=index"
              (click)="collectorDetail(collector,i)"
              [class.active]="i === selectedRow">
            <td>{{collector.nicNumber === "" ? "N/A" : collector.nicNumber}}</td>
            <td>{{collector.name === "" ? "N/A" : collector.name}}</td>
            <td>{{collector.address === "" ? "N/A" : collector.address}}</td>
            <td>{{collector.contactNo === "" ? "N/a" : collector.contactNo}}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <div class="row">
        <div class="col-md-12">
          <pagination
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            [maxSize]="pageSize"
            [itemsPerPage]="pageSize"
            (pageChanged)="pageChanged($event)"
            [maxSize]="10">
          </pagination>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <p> Total Collector : {{collectorCount}}</p>
          <div class="row">
            <div class="col-md-12 text-right">
              <button
                class="btn btn-primary pull-right " (click)="showDetails()" [disabled]="!selectedCollector"> Update
              </button>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>


