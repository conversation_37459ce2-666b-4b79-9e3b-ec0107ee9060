<div class="card">
  <div class="card-header">
    <strong>NEW COLLECTOR</strong>
  </div>

  <div class="card-body">
    <form #manageCollectorForm="ngForm" (ngSubmit)="saveCollector(); manageCollectorForm.reset() ">
      <div class="row">
        <div class="col-md-3 form-group">
          <label> NIC Number </label>
          <input type="text"
                 required
                 #nic="ngModel"
                 [class.is-invalid]="nic.invalid && nic.touched"
                 class="form-control"
                 id="nicNumber"
                 [(ngModel)]="collector.nicNumber"
                 name="nicNumber"
                 (keyup)="checkNicNo()"
                 placeholder=" NIC Number ">
          <div *ngIf="nic.errors && (nic.invalid || nic.touched)">
            <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">
              *NIC Number is required
            </small>
          </div>
          <small *ngIf="nicNoAvailability" [class.is-none]="true" class="text-danger">
            * This NIC Number already added
          </small>
        </div>
        <div class="col-md-6 form-group">
          <label>Name </label>
          <input type="text"
                 required
                 #dName="ngModel"
                 [class.is-invalid]="dName.invalid && dName.touched"
                 class="form-control"
                 id="dName"
                 [(ngModel)]="collector.name"
                 name="dName"
                 placeholder=" Collector Name ">
          <div *ngIf="dName.errors && (dName.invalid || dName.touched)">
            <small class="text-danger" [class.d-none]="dName.valid || dName.untouched">
              *Collector Name is required
            </small>
          </div>
        </div>
        <div class="col-md-3 form-group">
          <label> Contact No </label>
          <input type="text"
                 required
                 #contactNo="ngModel"
                 [class.is-invalid]="contactNo.invalid && contactNo.touched"
                 class="form-control"
                 id="contactNo"
                 [(ngModel)]="collector.contactNo"
                 name="contactNo"
                 placeholder=" Contact Number ">
          <div *ngIf="contactNo.errors && (contactNo.invalid || contactNo.touched)">
            <small class="text-danger" [class.d-none]="contactNo.valid || contactNo.untouched">
              *contactNo is required
            </small>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-9 form-group">
          <label> Address </label>
          <input type="text"
                 #address="ngModel"
                 class="form-control"
                 id="address"
                 [(ngModel)]="collector.address"
                 name="bName"
                 placeholder=" Address ">
          <div *ngIf="address.errors && (address.invalid || address.touched)">
          </div>
        </div>
        <div class="col-md-3 form-group">
            <label> Route </label>
            <div class="form-group">
              <select class="form-control" required="true"
                      #routeSelect="ngModel"
                      id="routeSelect"
                      [(ngModel)]="selectedRoute"
                      name="routeSelect"
                      (ngModelChange)="setSelectedRout()">
                <option selected>Select Route No</option>
                <option *ngFor="let type of routeList" [ngValue]="type">{{type.name}}</option>
              </select>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="mb-3">
            <label class="form-label fw-semibold">
              <i class="fas fa-camera me-1"></i>Upload Photos
            </label>
            <ngx-dropzone (change)="onSelect($event)" [maxFileSize]="2e+6"
                          class="custom-dropzone">
              <ngx-dropzone-label>
                <div class="text-center p-4">
                  <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                  <p class="mb-1">Drop images here or click to browse</p>
                  <small class="text-muted">Maximum file size: 2MB</small>
                </div>
              </ngx-dropzone-label>
              <ngx-dropzone-image-preview ngProjectAs="ngx-dropzone-preview"
                                          *ngFor="let f of files" [file]="f" [removable]="true"
                                          (removed)="onRemove($event)">
                <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
              </ngx-dropzone-image-preview>
            </ngx-dropzone>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12 mt-2">
          <div class="form-check checkbox mr-2">
            <input class="form-check-input"
                   id="check3"
                   name="check3"
                   type="checkbox"
                   value=""
                   [(ngModel)]="collector.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12 text-right">
          <button type="button" class="btn btn-warning mr-2" (click)="clear()">clear</button>
          <button type="submit" class="btn btn-success"
                  [disabled]="!manageCollectorForm.form.valid|| selectedCollector != null">save
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
