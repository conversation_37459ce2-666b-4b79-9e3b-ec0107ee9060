import {Component, OnInit} from '@angular/core';
import {Event, NavigationEnd, Router} from '@angular/router';
import {CommonService} from '../../service/common.service';

@Component({
  standalone: false,
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.css']
})
export class SideBarComponent implements OnInit {

  perms: Array<any> = [];
  isStarter = false;
  isCollapsed = false;
  user: any;
  depositChequeQty: number;
  currentRoute: string = '';

  constructor(private router: Router, private commonService: CommonService) {
    router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        this.currentRoute = event.url;
        this.loadRoutes();
      }
    });
    if (null === localStorage.getItem('currentUser')) {
      this.router.navigateByUrl('/login');
    } else {
      this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    }
  }

  ngOnInit() {
    // Load initial state from localStorage if available
    const sidebarState = localStorage.getItem('sidebarCollapsed');
    this.isCollapsed = sidebarState === 'true';
  }

  loadRoutes() {
    this.perms = [];
    let currentRoute = this.router.url;
    currentRoute = currentRoute.replace('/home/', '');
    if (currentRoute !== 'starter' && currentRoute !== '/login' && currentRoute !== 'dashboard') {
      this.commonService.findRelatedRoutes(currentRoute).subscribe((result: Array<any>) => {
        this.perms = result;
        this.isStarter = false;
      })
    } else {
      this.isStarter = true;
    }
  }

  loadView(url) {
    this.router.navigateByUrl('/home/' + url);
  }

  toggleSidebar() {
    this.isCollapsed = !this.isCollapsed;
    // Save state to localStorage
    localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
  }

  isActiveRoute(route: string): boolean {
    return this.currentRoute.includes(route);
  }
}
