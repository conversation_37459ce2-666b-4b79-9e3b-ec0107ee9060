import {Component, Inject, OnInit} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {Router} from '@angular/router';
import {RightSidebarService} from '../right-sidebar/right-sidebar.service';

@Component({
  standalone: false,
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit {

  elem;
  user: any;
  isRightSidebarOpen = false;

  constructor(@Inject(DOCUMENT) private document: any, private router: Router, private rightSidebarService: RightSidebarService) {
  }

  ngOnInit() {
    this.elem = document.documentElement;
    this.loadUserInfo();

    // Subscribe to right sidebar state changes
    this.rightSidebarService.isOpen$.subscribe(isOpen => {
      this.isRightSidebarOpen = isOpen;
    });
  }

  loadUserInfo() {
    if (localStorage.getItem('currentUser')) {
      this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    } else {
      this.router.navigateByUrl('/login');
    }
  }

  toggleRightSidebar() {
    this.rightSidebarService.toggle();
  }

  openFullscreen() {
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      this.elem.msRequestFullscreen();
    }
  }

}
