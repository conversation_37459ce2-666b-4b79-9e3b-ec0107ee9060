<div class="card shadow-sm border-0">
  <div class="card-header bg-primary text-white d-flex align-items-center">
    <i class="fas fa-user-plus me-2"></i>
    <h5 class="mb-0">User Registration</h5>
  </div>
  <div class="card-body p-4">
    <div class="row">
      <div class="col-md-12">
        <form (ngSubmit)="saveUser();userForm.reset()" #userForm="ngForm">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-user-tag me-1"></i>User Role
                </label>
                <select class="form-select" name="userRole" [(ngModel)]="userRole" (change)="selectRole(userRole)">
                  <option value="" disabled selected>Select a role</option>
                  <option *ngFor="let ur of userRoles" [ngValue]="ur">{{ur.name}}</option>
                </select>
                <div class="mt-2">
                  <tag-input [(ngModel)]="user.userRoles"
                             [identifyBy]="'id'" [displayBy]="'name'"
                             name="userRoles" [hideForm]="true"
                             [placeholder]="'Selected roles will appear here'"
                             (onRemove)="removeFromUserRoles($event)"
                             [theme]="'bootstrap'">
                  </tag-input>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-user me-1"></i>First Name <span class="text-danger">*</span>
                </label>
                <input type="text" required #firstName="ngModel"
                       [class.is-invalid]="firstName.invalid && firstName.touched"
                       class="form-control" id="firstName"
                       placeholder="Enter first name" name="firstName" [(ngModel)]="user.firstName">
                <div class="invalid-feedback" [class.d-none]="firstName.valid || firstName.untouched">
                  First Name is required
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-user me-1"></i>Last Name <span class="text-danger">*</span>
                </label>
                <input type="text" required #lastName="ngModel"
                       [class.is-invalid]="lastName.invalid && lastName.touched"
                       class="form-control" id="lastName"
                       placeholder="Enter last name" name="lastName" [(ngModel)]="user.lastName">
                <div class="invalid-feedback" [class.d-none]="lastName.valid || lastName.untouched">
                  Last Name is required
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-at me-1"></i>Username <span class="text-danger">*</span>
                </label>
                <input type="text" required #userName="ngModel"
                       [class.is-invalid]="userName.invalid && userName.touched || userAvailability"
                       class="form-control" id="userName" placeholder="Enter username" name="userName"
                       [(ngModel)]="user.username" (keyup)="checkUserName()">
                <div class="invalid-feedback" [class.d-none]="userName.valid || userName.untouched">
                  Username is required
                </div>
                <div *ngIf="userAvailability" class="text-danger small mt-1">
                  <i class="fas fa-exclamation-triangle me-1"></i>This username is already taken
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                </label>
                <input type="email" required #email="ngModel" [class.is-invalid]="email.invalid && email.touched"
                       class="form-control" id="email" placeholder="Enter email address" name="email"
                       [(ngModel)]="user.email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$">
                <div class="invalid-feedback" [class.d-none]="email.valid || email.untouched">
                  Please enter a valid email address
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-lock me-1"></i>Password <span class="text-danger">*</span>
                </label>
                <input type="password" #password="ngModel" [class.is-invalid]="password.invalid && password.touched"
                       class="form-control" id="password" placeholder="Enter 6 character password" name="password"
                       minlength="6" maxlength="6" size="6"
                       [(ngModel)]="user.password" required>
                <div class="invalid-feedback" [class.d-none]="password.valid || password.untouched">
                  Password must be exactly 6 characters
                </div>
                <div class="form-text">
                  <i class="fas fa-info-circle me-1"></i>Password must be exactly 6 characters
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-lock me-1"></i>Confirm Password <span class="text-danger">*</span>
                </label>
                <input type="password" class="form-control" id="confirmPassword" placeholder="Re-enter password"
                       maxlength="6" name="confirmPassword"
                       [class.is-invalid]="user.password !== confirmPassword && confirmPassword"
                       [(ngModel)]="confirmPassword" required (ngModelChange)="checkPassword()">
                <div class="invalid-feedback" [class.d-none]="user.password === confirmPassword || !confirmPassword">
                  Passwords do not match
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-5">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-cube me-1"></i>Modules
                </label>
                <select class="form-select" name="moduleList" [(ngModel)]="selectedModule"
                        (change)="findPermsForModule()">
                  <option value="" disabled selected>Select a module</option>
                  <option *ngFor="let mod of modules" [ngValue]="mod">{{mod.name}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-5">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-key me-1"></i>Permissions
                </label>
                <select class="form-select" name="permissionList" [(ngModel)]="selectedPermission">
                  <option value="" disabled selected>Select a permission</option>
                  <option *ngFor="let perm of selectedPermissions" [ngValue]="perm">{{perm.name}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="mb-3">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-success d-block w-100"
                        (click)="addPermissions(selectedPermission)"
                        [disabled]="!selectedPermission">
                  <i class="fas fa-plus me-1"></i>Add
                </button>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <div class="mb-3">
                <label class="form-label fw-semibold">
                  <i class="fas fa-list me-1"></i>Assigned Permissions
                </label>
                <tag-input [(ngModel)]="permissions"
                           [identifyBy]="'id'" [displayBy]="'name'"
                           name="modules" [hideForm]="true"
                           [placeholder]="'Selected permissions will appear here'"
                           [theme]="'bootstrap'">
                </tag-input>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input name="active" class="form-check-input" role="switch"
                       [(ngModel)]="user.active" id="active" type="checkbox">
                <label class="form-check-label fw-semibold" for="active">
                  <i class="fas fa-toggle-on me-1"></i>Active User
                </label>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12 text-end">
              <button type="button" class="btn btn-outline-secondary me-2"
                      (click)="clearForm()">
                <i class="fas fa-eraser me-1"></i>Clear
              </button>
              <button type="submit" class="btn btn-primary"
                      [disabled]="!userForm.form.valid || !isPasswordMatch">
                <i class="fas fa-save me-1"></i>Save User
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

