<div class="user-management-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-users"></i>
        User Management
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="findAllUsers()">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
      </div>
    </div>
    <p class="page-description">
      Manage user accounts and permissions for the loan management system.
    </p>
  </div>

  <div class="content-card">
    <!-- Search Section -->
    <div class="search-section">
      <div class="row">
        <div class="col-md-6">
          <div class="input-group">
            <div class="input-group-prepend">
              <span class="input-group-text bg-white"><i class="fas fa-search"></i></span>
            </div>
            <input type="text" [(ngModel)]="search" class="form-control" id="search"
                   placeholder="Enter username" name="search">
          </div>
        </div>
        <div class="col-md-2">
          <button type="button" class="btn btn-primary" (click)="searchUser()">
            <i class="fas fa-search"></i>
            Search
          </button>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="table-section">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Full Name</th>
              <th>User Name</th>
              <th>Email</th>
              <th>User Role</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of users ,let i = index" (click)="userDetail(user,i)"
                [class.table-active]="i === selectedRow"
                class="clickable-row">
              <td>
                <strong>{{user.firstName + ' ' + user.lastName}}</strong>
              </td>
              <td>{{user.username}}</td>
              <td>{{user.email}}</td>
              <td>
                <span class="badge badge-primary mr-1" *ngFor="let userRole of user.userRoles">
                  {{userRole.name}}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
      <div class="text-right">
        <button class="btn btn-primary" (click)="editUser(templateEditUser)" [disabled]="!isUserSelected">
          <i class="fas fa-edit"></i>
          Edit User
        </button>
      </div>
    </div>
  </div>
</div>

<ng-template #templateEditUser>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Edit User</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-create-user></app-create-user>
  </div>
</ng-template>
