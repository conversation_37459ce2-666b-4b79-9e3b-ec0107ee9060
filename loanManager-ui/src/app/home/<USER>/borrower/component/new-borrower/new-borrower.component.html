<div class="modern-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-user-plus"></i>
        {{isEdit ? 'Edit' : 'New'}} Borrower
      </h2>
      <div class="header-actions">
        <button type="button" class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
          <i class="fas fa-sync-alt"></i>
          Reset
        </button>
      </div>
    </div>
    <p class="page-description">
      {{isEdit ? 'Update borrower information and details.' : 'Add a new borrower to the system with personal and contact information.'}}
    </p>
  </div>

  <!-- Form Card -->
  <div class="form-card">
    <div class="form-header">
      <h5>
        <i class="fas fa-edit"></i>
        Borrower Information
      </h5>
    </div>
    <div class="form-body">
    <form #newPersonForm="ngForm" (ngSubmit)="savePerson(newPersonForm);">
      <div class="row">
        <div class="form-group col-md-6">
          <label>NIC</label>
          <input required #nic="ngModel" (keyup)="checkNic()" type="text" name="nic" id="nic"
                 [(ngModel)]="borrower.nic"
                 [class.is-invalid]="nic.invalid && nic.touched"
                 pattern="([0-9]{9}[x|X|v|V]|[0-9]{12})$"
                 class="form-control" placeholder="Enter NIC No" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">* NIC No is required
          </small>

          <small *ngIf="nicAvailability" [class.is-none]="true" class="text-danger">* NIC No
            already used
          </small>
        </div>

        <div class="form-group col-md-6">
          <label>Name </label>
          <input type="text" required #CuName="ngModel" [class.is-invalid]="CuName.invalid && CuName.touched"
                 class="form-control" id="CuName" [(ngModel)]="borrower.name" name="CuName"
                 placeholder="Enter Name " [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="CuName.valid || CuName.untouched">* Name is required
          </small>
        </div>
        <div class="form-group col-md-6">
          <label>Address</label>
          <input type="text" required #address1="ngModel" class="form-control" id="address1"
                 name="address1" placeholder="Enter Address"
                 [class.is-invalid]="address1.invalid && address1.touched"
                 [(ngModel)]="borrower.address">
          <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">*Address is
            required
          </small>
        </div>

        <div class="form-group col-md-4">
          <label>Contact Number 1</label>
          <input required #telephone1="ngModel" [class.is-invalid]="telephone1.invalid && telephone1.touched"
                 type="text" class="form-control" id="telephone1" placeholder="Enter Contact Number 1"
                 name="telephone1"
                 pattern="^\d{10}$"
                 [(ngModel)]="borrower.telephone1">
          <small class="text-danger" [class.d-none]="telephone1.valid || telephone1.untouched">*Contact Number 1 is
            required
          </small>
        </div>
        <div class="form-group col-md-4">
          <label>Contact Number 2 </label>
          <input type="text" class="form-control" id="telephone2" placeholder="Enter Contact Number 2"
                 name="telephone2"
                 pattern="^\d{10}$"
                 [(ngModel)]="borrower.telephone2">
        </div>
        <div class="col-md-4 form-group">
          <label> Route </label>
          <div class="form-group">
            <select class="form-control" required="true"
                    #routeSelect="ngModel"
                    id="routeSelect"
                    [(ngModel)]="selectedRoute"
                    name="routeSelect"
                    (ngModelChange)="setSelectedRout()">
              <option selected>Select Route No</option>
              <option *ngFor="let type of routeList" [ngValue]="type">{{type.name}}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="mb-3">
            <label class="form-label fw-semibold">
              <i class="fas fa-camera me-1"></i>Upload Photos
            </label>
            <ngx-dropzone (change)="onSelect($event)" [maxFileSize]="2e+6"
                          class="custom-dropzone">
              <ngx-dropzone-label>
                <div class="text-center p-4">
                  <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                  <p class="mb-1">Drop images here or click to browse</p>
                  <small class="text-muted">Maximum file size: 2MB</small>
                </div>
              </ngx-dropzone-label>
              <ngx-dropzone-image-preview ngProjectAs="ngx-dropzone-preview"
                                          *ngFor="let f of files" [file]="f" [removable]="true"
                                          (removed)="onRemove($event)">
                <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
              </ngx-dropzone-image-preview>
            </ngx-dropzone>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <div class="row">
          <div class="col-md-12 text-right">
            <button type="button" class="btn btn-outline-secondary mr-2" (click)="clear();" [disabled]="isEdit">
              <i class="fas fa-eraser"></i>
              Clear
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="isSubmitting || !newPersonForm.form.valid || nicAvailability">
              <i class="fas fa-save"></i>
              {{isEdit ? 'Update' : 'Save'}} Borrower
            </button>
          </div>
        </div>
      </div>
    </form>
    </div>
  </div>
</div>

