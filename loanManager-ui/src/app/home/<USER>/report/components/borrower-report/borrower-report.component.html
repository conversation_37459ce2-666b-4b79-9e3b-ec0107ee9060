<div class="modern-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-chart-bar"></i>
        Borrower Report
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="refreshData()" [disabled]="loading">
          <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
          Refresh
        </button>
        <button class="btn btn-success btn-sm" (click)="exportReport()">
          <i class="fas fa-download"></i>
          Export
        </button>
      </div>
    </div>
    <p class="page-description">
      Comprehensive report showing borrower information, loan history, and payment status.
    </p>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-primary">
        <div class="summary-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="summary-content">
          <h4>{{totalBorrowers}}</h4>
          <p>Total Borrowers</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-danger">
        <div class="summary-icon">
          <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="summary-content">
          <h4>{{totalLoanAmount | number:'1.2-2'}}</h4>
          <p>Total Loan Amount</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-success">
        <div class="summary-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="summary-content">
          <h4>{{totalPaidAmount | number:'1.2-2'}}</h4>
          <p>Total Paid</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="summary-card summary-warning">
        <div class="summary-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="summary-content">
          <h4>{{totalOutstandingAmount | number:'1.2-2'}}</h4>
          <p>Outstanding Amount</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="filters-card mb-4">
    <div class="filters-header">
      <h6>
        <i class="fas fa-filter"></i>
        Filter Options
      </h6>
    </div>
    <div class="filters-body">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label for="searchTerm">Search</label>
            <input 
              type="text" 
              id="searchTerm"
              class="form-control form-control-sm" 
              [(ngModel)]="searchTerm"
              (keyup)="applyFilters()"
              placeholder="Name, NIC, Phone...">
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label for="statusFilter">Status</label>
            <select 
              id="statusFilter"
              class="form-control form-control-sm" 
              [(ngModel)]="statusFilter"
              (change)="applyFilters()">
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="defaulted">Defaulted</option>
            </select>
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label for="dateFrom">Date From</label>
            <input 
              type="date" 
              id="dateFrom"
              class="form-control form-control-sm" 
              [(ngModel)]="dateFrom"
              (change)="applyFilters()">
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label for="dateTo">Date To</label>
            <input 
              type="date" 
              id="dateTo"
              class="form-control form-control-sm" 
              [(ngModel)]="dateTo"
              (change)="applyFilters()">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>&nbsp;</label>
            <button class="btn btn-outline-secondary btn-sm btn-block" (click)="clearFilters()">
              <i class="fas fa-times"></i>
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Borrowers Table -->
  <div class="table-card">
    <div class="table-header">
      <h6>
        <i class="fas fa-table"></i>
        Borrower Details
      </h6>
      <span class="badge badge-primary">{{filteredBorrowers.length}} borrowers</span>
    </div>
    <div class="table-body">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-2">Loading borrower report...</p>
      </div>

      <!-- Borrowers Table -->
      <div *ngIf="!loading && filteredBorrowers.length > 0" class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Name</th>
              <th>NIC</th>
              <th>Contact</th>
              <th>Total Loans</th>
              <th>Loan Amount</th>
              <th>Paid Amount</th>
              <th>Outstanding</th>
              <th>Last Loan</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let borrower of filteredBorrowers | slice:(page-1)*pageSize:page*pageSize">
              <td>
                <strong>{{borrower.firstName}} {{borrower.lastName}}</strong>
                <small class="d-block text-muted">{{borrower.address}}</small>
              </td>
              <td>{{borrower.nic}}</td>
              <td>{{borrower.phoneNumber}}</td>
              <td>
                <span class="badge badge-info">{{borrower.totalLoans}}</span>
              </td>
              <td>{{borrower.totalAmount | number:'1.2-2'}}</td>
              <td class="text-success">{{borrower.totalPaid | number:'1.2-2'}}</td>
              <td class="text-danger">{{borrower.totalOutstanding | number:'1.2-2'}}</td>
              <td>{{borrower.lastLoanDate | date:'mediumDate'}}</td>
              <td>
                <span class="badge" [ngClass]="getStatusBadgeClass(borrower.status)">
                  {{borrower.status | titlecase}}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="!loading && filteredBorrowers.length === 0" class="no-data-container">
        <div class="no-data-content">
          <i class="fas fa-users"></i>
          <h5>No Borrowers Found</h5>
          <p>No borrowers match your current filter criteria. Try adjusting the filters.</p>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && filteredBorrowers.length > pageSize" class="pagination-container">
        <ngb-pagination 
          [(page)]="page" 
          [pageSize]="pageSize" 
          [collectionSize]="collectionSize"
          [maxSize]="5"
          [rotate]="true"
          (pageChange)="pageChanged($event)">
        </ngb-pagination>
      </div>
    </div>
  </div>
</div>
