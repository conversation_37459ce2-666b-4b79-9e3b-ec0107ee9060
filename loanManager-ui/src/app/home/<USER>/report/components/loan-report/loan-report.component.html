<div class="modern-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-chart-line"></i>
        Loan Report
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="ngOnInit()">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
        <button class="btn btn-success btn-sm" (click)="exportReport()">
          <i class="fas fa-download"></i>
          Export
        </button>
      </div>
    </div>
    <p class="page-description">
      Comprehensive loan report with filtering and analysis capabilities.
    </p>
  </div>

  <!-- Filters Card -->
  <div class="filters-card">
    <div class="filters-header">
      <h6>
        <i class="fas fa-filter"></i>
        Filter Options
      </h6>
    </div>
    <div class="filters-body">
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            <label for="statusFilter">Filter By Status</label>
            <select class="form-control" name="loanPlan" id="statusFilter" #statusMod="ngModel"
                    [(ngModel)]="selectedStatus" (ngModelChange)="findByStatus()">
              <option value="">All Status</option>
              <option *ngFor="let status of statusList" [ngValue]="status">{{ status.value }}</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loans Table -->
  <div class="table-card">
    <div class="table-header">
      <h6>
        <i class="fas fa-table"></i>
        Loans Report
      </h6>
      <span class="badge badge-primary">{{loans?.length || 0}} loans</span>
    </div>
    <div class="table-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Borrower Name</th>
              <th>Loan Plan</th>
              <th>Loan Amount</th>
              <th>Balance</th>
              <th>Status</th>
              <th>Loan Date</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let loan of loans,let i = index">
              <td>
                <strong>{{ loan.borrower?.name || 'N/A' }}</strong>
                <small class="d-block text-muted" *ngIf="loan.borrower?.nic">{{ loan.borrower.nic }}</small>
              </td>
              <td>{{ loan.loanPlan?.name || 'N/A' }}</td>
              <td>{{ loan.loanAmount | number:'1.2-2' }}</td>
              <td>{{ loan.balance | number:'1.2-2' }}</td>
              <td>
                <span class="badge" [ngClass]="getStatusBadgeClass(loan.status?.value)">
                  {{ loan.status?.value || "N/A" }}
                </span>
              </td>
              <td>{{ loan.dateTime | date:'mediumDate' }}</td>
            </tr>
          </tbody>
        </table>

      <!-- Pagination -->
      <div class="pagination-container">
        <pagination class="pagination-sm justify-content-center"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>

  <!-- Summary Card -->
  <div class="summary-card">
    <div class="summary-header">
      <h6>
        <i class="fas fa-chart-bar"></i>
        Loan Summary
      </h6>
    </div>
    <div class="summary-body">
      <div class="row">
        <div class="col-md-6">
          <div class="summary-item">
            <span class="summary-label">Total Loan Amount:</span>
            <span class="summary-value text-primary">{{ totalAmount | number: '1.2-2' }}</span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="summary-item">
            <span class="summary-label">Total Amount to Collect:</span>
            <span class="summary-value text-success">{{ totalAmountToCollect | number: '1.2-2' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

