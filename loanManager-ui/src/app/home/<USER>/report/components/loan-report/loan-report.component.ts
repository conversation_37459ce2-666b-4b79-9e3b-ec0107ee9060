import {Component, OnInit} from '@angular/core';
import {BsModalRef} from "ngx-bootstrap/modal";
import {Loan} from "../../../business/model/loan";
import {LoanService} from "../../../business/service/loanService";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './loan-report.component.html',
  styleUrls: ['./loan-report.component.css']
})
export class LoanReportComponent implements OnInit {

  modalRef: BsModalRef;
  loans: Array<Loan> = [];

  selectedStatus: MetaData;
  statusList: Array<MetaData> = [];

  totalAmount: number;
  totalAmountToCollect: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 15;
    this.findAllStatus();
    this.getCurrentLoans();
  }

  getCurrentLoans() {
    this.metaDataService.findByValueAndCategory("Current", "Loan Status").subscribe((data: any) => {
      this.selectedStatus = data;
      this.findByStatus();
    });
  }

  findAllStatus() {
    this.metaDataService.findByCategory("Loan Status").subscribe((data: any) => {
      this.statusList = data;
    });
  }

  findByStatus() {
    this.loanService.findByStatus(this.selectedStatus.id,this.page - 1, this.pageSize).subscribe((data: any) => {
      this.loans = [];
      this.loans = data.content;
      this.collectionSize = data.totalElements;
      this.calcTotalAmount();
    });
  }

  calcTotalAmount() {
    this.totalAmount = 0;
    this.totalAmountToCollect = 0;
    for (let loan of this.loans) {
      this.totalAmount = this.totalAmount + loan.loanAmount;
      this.totalAmountToCollect = this.totalAmountToCollect + loan.balance;
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.getCurrentLoans();
  }

  exportReport(): void {
    // Implementation for exporting to CSV/Excel
    console.log('Export functionality to be implemented');
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'badge-success';
      case 'settled':
        return 'badge-info';
      case 'defaulted':
        return 'badge-danger';
      case 'pending':
        return 'badge-warning';
      default:
        return 'badge-secondary';
    }
  }

}
