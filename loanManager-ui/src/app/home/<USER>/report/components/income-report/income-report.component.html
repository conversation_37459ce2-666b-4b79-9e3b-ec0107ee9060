<div class="modern-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-chart-area"></i>
        Income Report
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="refreshData()" [disabled]="loading">
          <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
          Refresh
        </button>
        <button class="btn btn-success btn-sm" (click)="exportReport()">
          <i class="fas fa-download"></i>
          Export
        </button>
      </div>
    </div>
    <p class="page-description">
      Detailed income analysis showing revenue streams, expenses, and profitability metrics.
    </p>
  </div>

  <!-- Analytics Cards -->
  <div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="analytics-card analytics-revenue">
        <div class="analytics-icon">
          <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="analytics-content">
          <h4>{{analytics.totalRevenue | number:'1.2-2'}}</h4>
          <p>Total Revenue</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="analytics-card analytics-expenses">
        <div class="analytics-icon">
          <i class="fas fa-minus-circle"></i>
        </div>
        <div class="analytics-content">
          <h4>{{analytics.totalExpenses | number:'1.2-2'}}</h4>
          <p>Total Expenses</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="analytics-card analytics-profit">
        <div class="analytics-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="analytics-content">
          <h4 [class.text-success]="analytics.netProfit >= 0" [class.text-danger]="analytics.netProfit < 0">
            {{analytics.netProfit | number:'1.2-2'}}
          </h4>
          <p>Net Profit</p>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="analytics-card analytics-margin">
        <div class="analytics-icon">
          <i class="fas fa-percentage"></i>
        </div>
        <div class="analytics-content">
          <h4 [class.text-success]="analytics.profitMargin >= 0" [class.text-danger]="analytics.profitMargin < 0">
            {{analytics.profitMargin | number:'1.1-1'}}%
          </h4>
          <p>Profit Margin</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Analytics -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="info-card">
        <div class="info-header">
          <h6><i class="fas fa-calendar-day"></i> Daily Averages</h6>
        </div>
        <div class="info-body">
          <div class="info-item">
            <span class="info-label">Average Daily Income:</span>
            <span class="info-value">{{analytics.averageDailyIncome | number:'1.2-2'}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="info-card">
        <div class="info-header">
          <h6><i class="fas fa-trophy"></i> Performance Highlights</h6>
        </div>
        <div class="info-body">
          <div class="info-item">
            <span class="info-label">Highest Income Day:</span>
            <span class="info-value">{{analytics.highestIncomeDay | date:'mediumDate'}}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Lowest Income Day:</span>
            <span class="info-value">{{analytics.lowestIncomeDay | date:'mediumDate'}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="filters-card mb-4">
    <div class="filters-header">
      <h6>
        <i class="fas fa-filter"></i>
        Report Filters
      </h6>
    </div>
    <div class="filters-body">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label for="dateFrom">Date From</label>
            <input
              type="date"
              id="dateFrom"
              class="form-control form-control-sm"
              [(ngModel)]="dateFrom">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="dateTo">Date To</label>
            <input
              type="date"
              id="dateTo"
              class="form-control form-control-sm"
              [(ngModel)]="dateTo">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="reportType">Report Type</label>
            <select
              id="reportType"
              class="form-control form-control-sm"
              [(ngModel)]="reportType">
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>&nbsp;</label>
            <div class="btn-group btn-block">
              <button class="btn btn-primary btn-sm" (click)="applyFilters()">
                <i class="fas fa-search"></i>
                Apply
              </button>
              <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                <i class="fas fa-times"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Income Summary -->
  <div class="summary-card mb-4">
    <div class="summary-header">
      <h6>
        <i class="fas fa-chart-pie"></i>
        Income Breakdown
      </h6>
    </div>
    <div class="summary-body">
      <div class="row">
        <div class="col-md-3">
          <div class="breakdown-item">
            <span class="breakdown-label">Loan Payments:</span>
            <span class="breakdown-value text-primary">{{getTotalLoanPayments() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="breakdown-item">
            <span class="breakdown-label">Interest Earned:</span>
            <span class="breakdown-value text-success">{{getTotalInterestEarned() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="breakdown-item">
            <span class="breakdown-label">Penalty Charges:</span>
            <span class="breakdown-value text-warning">{{getTotalPenaltyCharges() | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="breakdown-item">
            <span class="breakdown-label">Other Income:</span>
            <span class="breakdown-value text-info">{{getTotalOtherIncome() | number:'1.2-2'}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Income Data Table -->
  <div class="table-card">
    <div class="table-header">
      <h6>
        <i class="fas fa-table"></i>
        Income Details
      </h6>
      <span class="badge badge-primary">{{incomeData.length}} records</span>
    </div>
    <div class="table-body">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-2">Loading income report...</p>
      </div>

      <!-- Income Table -->
      <div *ngIf="!loading && incomeData.length > 0" class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Date</th>
              <th>Loan Payments</th>
              <th>Interest Earned</th>
              <th>Penalty Charges</th>
              <th>Other Income</th>
              <th>Total Income</th>
              <th>Expenses</th>
              <th>Net Income</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of incomeData | slice:(page-1)*pageSize:page*pageSize">
              <td>{{item.date | date:'mediumDate'}}</td>
              <td class="text-primary">{{item.loanPayments | number:'1.2-2'}}</td>
              <td class="text-success">{{item.interestEarned | number:'1.2-2'}}</td>
              <td class="text-warning">{{item.penaltyCharges | number:'1.2-2'}}</td>
              <td class="text-info">{{item.otherIncome | number:'1.2-2'}}</td>
              <td class="font-weight-bold">{{item.totalIncome | number:'1.2-2'}}</td>
              <td class="text-danger">{{item.expenses | number:'1.2-2'}}</td>
              <td [class.text-success]="item.netIncome >= 0" [class.text-danger]="item.netIncome < 0" class="font-weight-bold">
                {{item.netIncome | number:'1.2-2'}}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="!loading && incomeData.length === 0" class="no-data-container">
        <div class="no-data-content">
          <i class="fas fa-chart-area"></i>
          <h5>No Income Data Found</h5>
          <p>No income data available for the selected date range. Try adjusting the filters.</p>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && incomeData.length > pageSize" class="pagination-container">
        <pagination
          [(page)]="page"
          [pageSize]="pageSize"
          [collectionSize]="collectionSize"
          [maxSize]="5"
          [rotate]="true"
          (pageChange)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>
</div>
