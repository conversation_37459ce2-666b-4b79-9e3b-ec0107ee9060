<div class="payment-report-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="fas fa-file-invoice-dollar"></i>
        Payment Report
      </h2>
      <div class="header-actions">
        <button class="btn btn-outline-primary btn-sm" (click)="loadTodayPayments()">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
      </div>
    </div>
    <p class="page-description">
      View and analyze payment transactions across different date ranges.
    </p>
  </div>

  <div class="content-card">
    <!-- Filter Section -->
    <div class="filter-section">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label for="fromDate">From Date</label>
            <input [(ngModel)]="fromDate" bsDatepicker [bsConfig]="{dateInputFormat: 'YYYY-MM-DD' }"
                   autocomplete="off"
                   placeholder="Select from date"
                   class="form-control" name="fromDate" id="fromDate">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="toDate">To Date</label>
            <input [(ngModel)]="toDate" bsDatepicker [bsConfig]="{dateInputFormat: 'YYYY-MM-DD' }"
                   autocomplete="off"
                   placeholder="Select to date"
                   class="form-control" name="toDate" id="toDate">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>&nbsp;</label>
            <button class="btn btn-primary d-block" (click)="searchByDateRange()">
              <i class="fas fa-search"></i>
              Search
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section" *ngIf="payments.length > 0">
      <div class="summary-card">
        <div class="summary-content">
          <h4>{{totalAmount | number:'1.2-2'}}</h4>
          <p>Total Payment Amount</p>
        </div>
        <div class="summary-icon">
          <i class="fas fa-money-bill-wave"></i>
        </div>
      </div>
    </div>

    <!-- Payments Table -->
    <div class="table-section">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Date</th>
              <th>Loan No</th>
              <th>Loan Plan</th>
              <th>Borrower</th>
              <th>Payment Amount</th>
              <th>Route</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let payment of payments,let i = index">
              <td>{{payment.dateTime | date:'mediumDate'}}</td>
              <td>
                <strong>{{payment.loanNo}}</strong>
              </td>
              <td>{{payment.loanPlan}}</td>
              <td>{{payment.borrowerName}}</td>
              <td>
                <span class="amount-display">{{payment.amount | number:'1.2-2'}}</span>
              </td>
              <td>
                <span class="badge badge-info">{{payment.routeName}}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="payments.length === 0" class="no-data-message">
        <div class="text-center py-5">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">No payments found</h5>
          <p class="text-muted">Try adjusting your date range or check back later.</p>
        </div>
      </div>
    </div>
  </div>
</div>

