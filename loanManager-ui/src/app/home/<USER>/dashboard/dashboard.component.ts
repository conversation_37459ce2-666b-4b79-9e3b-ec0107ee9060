import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../service/dashboard.service';

export interface LoanCountStat {
  settledLoanCount: number;
  activeLoanCount: number;
  arrearsLoanCount: number;
  totalLoanCount: number;
}

export interface MonthlyPaymentStats {
  monthlyPaymentAmount: number;
  monthlyExpenseAmount: number;
  monthlyProfit: number;
}

export interface LoanStat {
  amountLent: number;
  amountCollected: number;
  collectibleAmount: number;
  profit: number;
}

@Component({
  standalone: false,
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {

  loanCountStat: LoanCountStat = {
    settledLoanCount: 0,
    activeLoanCount: 0,
    arrearsLoanCount: 0,
    totalLoanCount: 0
  };

  monthlyStats: MonthlyPaymentStats = {
    monthlyPaymentAmount: 0,
    monthlyExpenseAmount: 0,
    monthlyProfit: 0
  };

  ongoingLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  settledLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  allLoanStat: LoanStat = {
    amountLent: 0,
    amountCollected: 0,
    collectibleAmount: 0,
    profit: 0
  };

  cashInHand: number = 0;
  loading: boolean = true;
  user: any;

  constructor(private dashboardService: DashboardService) {
    this.user = JSON.parse(localStorage.getItem('currentUser') || '{}').user || {};
  }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.loading = true;

    // Load all dashboard data
    Promise.all([
      this.getMonthlyStat(),
      this.getOngoingLoanStats(),
      this.getLoanCountStat(),
      this.getSettledLoanStat(),
      this.getAllLoanStat(),
      this.getCashInHand()
    ]).finally(() => {
      this.loading = false;
    });
  }

  getOngoingLoanStats(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getOngoingLoanStats().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.ongoingLoanStat = data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getLoanCountStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getLoanCountStats().subscribe({
        next: (data: LoanCountStat) => {
          if (data) {
            this.loanCountStat = data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getMonthlyStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getMonthlyStats().subscribe({
        next: (data: MonthlyPaymentStats) => {
          if (data) {
            this.monthlyStats = data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getSettledLoanStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getSettledStats().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.settledLoanStat = data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getAllLoanStat(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getAllLoanStat().subscribe({
        next: (data: LoanStat) => {
          if (data) {
            this.allLoanStat = data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getCashInHand(): Promise<void> {
    return new Promise((resolve) => {
      this.dashboardService.getCashInHand().subscribe({
        next: (data: any) => {
          if (data) {
            this.cashInHand = data.currentBalance || 0;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  refreshData(): void {
    this.loadDashboardData();
  }
}
