@echo off
REM Simple script to test multi-tenancy setup
REM This script helps verify that the multi-tenancy implementation is working

echo ========================================
echo Multi-Tenancy Test Script
echo ========================================
echo.

REM Check if application is running
echo 1. Testing application connectivity...
curl -s http://localhost:8080/hotel-service/api/tenant/info > nul
if %errorlevel% neq 0 (
    echo ❌ Application is not running on localhost:8080
    echo Please start the application first
    pause
    exit /b 1
)
echo ✅ Application is running

echo.
echo 2. Testing default tenant (localhost)...
curl -s http://localhost:8080/hotel-service/api/tenant/info
echo.

echo.
echo 3. Testing with demo subdomain...
echo Note: This requires adding '127.0.0.1 demo.localhost' to your hosts file
curl -s -H "Host: demo.localhost:8080" http://localhost:8080/hotel-service/api/tenant/info
echo.

echo.
echo 4. Testing with client1 subdomain...
curl -s -H "Host: client1.localhost:8080" http://localhost:8080/hotel-service/api/tenant/info
echo.

echo.
echo 5. Debug information...
curl -s http://localhost:8080/hotel-service/api/tenant/debug
echo.

echo.
echo ========================================
echo Test completed!
echo ========================================
echo.
echo To test with real subdomains:
echo 1. Add entries to your hosts file:
echo    127.0.0.1 demo.localhost
echo    127.0.0.1 client1.localhost
echo.
echo 2. Access in browser:
echo    http://demo.localhost:8080/hotel-service/api/tenant/info
echo    http://client1.localhost:8080/hotel-service/api/tenant/info
echo.
pause
