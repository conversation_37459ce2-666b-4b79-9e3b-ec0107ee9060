# general

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 12.0.0.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

## Installation Notes

This project uses Angular 19.x and Bootstrap 5.3.0 for UI components.

### Installing Dependencies

Run `npm install` to install all dependencies.

### Loading Indicators

The project uses Bootstrap 5 spinners for loading indicators. The implementation includes:

1. A full-screen overlay with a centered spinner for blocking operations
2. Inline spinners for non-blocking operations

Example usage:
```html
<!-- Full-screen loading overlay -->
<div *ngIf="loading" class="position-fixed w-100 h-100 d-flex justify-content-center align-items-center" 
     style="top: 0; left: 0; background-color: rgba(0,0,0,0.3); z-index: 1050;">
  <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<!-- Inline spinner -->
<div *ngIf="loading" class="text-center py-4">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
```
