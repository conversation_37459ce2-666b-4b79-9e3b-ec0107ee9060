import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthenticationService } from '../home/<USER>/service/authentication.service';
import { AppStateService } from '../home/<USER>/service/app-state.service';
import { Router } from '@angular/router';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(
    private authenticationService: AuthenticationService,
    private router: Router,
    private appStateService: AppStateService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError(err => {
        switch (err.status) {
          case 404:
            if (!this.router.url.includes('/404')) {
              this.router.navigate(['/404']);
            }
            break;
          case 500:
            if (!this.router.url.includes('/500')) {
              this.router.navigate(['/500']);
            }
            break;
          case 401:
            this.appStateService.handleAuthError();
            break;
          case 0:
            console.error("Network or CORS error:", err);
            break;
          default:
            if (!this.router.url.includes('/error')) {
              this.router.navigate(['/error'], { queryParams: { code: err.status } });
            }
            break;
        }

        const error = { message: err.error.message || err.statusText, status: err.status };
        return throwError(error);
      })
    );
  }


}
