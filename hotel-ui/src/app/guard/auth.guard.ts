import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {

    // Check if user is logged in
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');

    if (currentUser && currentUser.token) {
      // Check if token is expired (basic check)
      try {
        const tokenPayload = JSON.parse(atob(currentUser.token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        if (tokenPayload.exp && tokenPayload.exp < currentTime) {
          // Token is expired
          localStorage.removeItem('currentUser');
          this.router.navigate(['/login']);
          return false;
        }

        return true;
      } catch (error) {
        // Invalid token format
        localStorage.removeItem('currentUser');
        this.router.navigate(['/login']);
        return false;
      }
    } else {
      // No user or token
      this.router.navigate(['/login']);
      return false;
    }
  }
}
