<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Employee Search & Table -->
    <div class="col-lg-12">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Manage Employees</h5>
            <div class="input-group" style="max-width: 300px;">
              <input [(ngModel)]="keyEmployee"
                     [typeahead]="employees"
                     (typeaheadLoading)="loadEmployees()"
                     (typeaheadOnSelect)="setSelectedEmployee($event)"
                     [typeaheadOptionsLimit]="15"
                     typeaheadOptionField="name"
                     placeholder="Search Employee"
                     autocomplete="off"
                     class="form-control"
                     name="brand">
              <button class="btn btn-primary" type="button">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive table-height">
            <table class="table table-professional table-hover table-striped mb-0 text-center">
              <thead class="table-light">
              <tr>
                <th>Employee Name</th>
                <th>NIC</th>
                <th>Contact Number</th>
                <th>Department</th>
                <th>Designation</th>
                <th>Salary Scale</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let employee of employees; let i = index"
                  class="table-row"
                  [class.active]="i === selectedRow"
                  (click)="employeeDetail(employee, i)">
                <td class="align-middle">{{ employee.name }}</td>
                <td class="align-middle">{{ employee.nic }}</td>
                <td class="align-middle">{{ employee.telephone1 }}</td>
                <td class="align-middle">{{ employee.department ? employee.department.departmentName : 'N/A'}}</td>
                <td class="align-middle">{{ employee.designation ? employee.designation.designationName : 'N/A'}}</td>
                <td class="align-middle">{{ employee.salaryScale ? employee.salaryScale.name :  'N/A'}}</td>
              </tr>
              <tr *ngIf="employees?.length === 0">
                <td colspan="100%" class="text-center empty-state-professional">
                  <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                  No employees found
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > 0">
          <div class="d-flex justify-content-center">
            <pagination class="pagination pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12 text-end">
      <button class="btn btn-primary me-2"
              type="button"
              (click)="openModal(true)"
              [disabled]="selectedRow === null">
        View
      </button>
      <button class="btn btn-primary"
              type="button"
              (click)="openModal(false)"
              [disabled]="selectedRow === null">
        Edit
      </button>
    </div>
  </div>
</div>

<!-- Modal Template -->
<ng-template #employeeUpdateModal>
  <div class="modal-header">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>
  <div class="modal-body">
    <app-employee></app-employee>
  </div>
</ng-template>
