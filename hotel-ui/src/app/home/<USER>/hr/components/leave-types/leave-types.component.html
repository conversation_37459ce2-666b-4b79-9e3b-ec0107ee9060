<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Leave Types Table Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">Leave Types</h5>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive table-height">
            <table class="table table-professional table-hover table-striped mb-0 text-center">
              <thead class="table-light">
              <tr>
                <th>Type Name</th>
                <th>No of Leaves</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let addLeave of addLeaves; let i = index"
                  class="table-row"
                  [class.active]="i === selectedRow"
                  style="cursor: pointer;"
                  (click)="leaveDetail(addLeave); setClickedRow(i)">
                <td class="align-middle">{{ addLeave.leaveType }}</td>
                <td class="align-middle">{{ addLeave.no }}</td>
              </tr>
              <tr *ngIf="addLeaves?.length === 0">
                <td colspan="100%" class="text-center empty-state-professional">
                  <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                  No leave types found
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > 0">
          <div class="d-flex justify-content-center">
            <pagination class="pagination pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Leave Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">{{ addLeave.id ? 'Edit Leave Type' : 'Add Leave Type' }}</h5>
        </div>

        <div class="card-body professional-card-body">
          <form #AddLeave="ngForm" (ngSubmit)="save(AddLeave)">
            <!-- Leave Type Input -->
            <div class="mb-3">
              <label for="lType" class="form-label fw-semibold">Leave Type</label>
              <input type="text"
                     id="lType"
                     name="lType"
                     class="form-control"
                     placeholder="Enter Leave Type"
                     required
                     [(ngModel)]="addLeave.leaveType"
                     #lType="ngModel"
                     [class.is-invalid]="lType.invalid && lType.touched"
                     (keyup)="invalidAdd()">
              <div *ngIf="lType.invalid && (lType.dirty || lType.touched)" class="invalid-feedback">
                Leave type is required.
              </div>
              <small *ngIf="invalidAddLeave" class="text-danger">* Leave name is already used</small>
            </div>

            <!-- Number of Leaves Input -->
            <div class="mb-3">
              <label for="noL" class="form-label fw-semibold">No of Leaves</label>
              <input type="number"
                     id="noL"
                     name="noL"
                     class="form-control"
                     placeholder="Enter No of Leaves"
                     required
                     [(ngModel)]="addLeave.no"
                     #noL="ngModel"
                     [class.is-invalid]="noL.invalid && noL.touched">
              <div *ngIf="noL.invalid && (noL.dirty || noL.touched)" class="invalid-feedback">
                Number of leaves is required.
              </div>
            </div>

            <!-- Active Checkbox -->
            <div class="form-check mb-3 ms-1">
              <input type="checkbox"
                     class="form-check-input"
                     id="check3"
                     name="check3"
                     [(ngModel)]="addLeave.active">
              <label class="form-check-label fw-semibold" for="check3">Is Active</label>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2 justify-content-end flex-wrap">
              <button type="submit"
                      class="btn btn-primary"
                      [disabled]="!AddLeave.form.valid || invalidAddLeave">
                Save
              </button>
              <button type="button"
                      class="btn btn-warning"
                      (click)="Clear()">
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
