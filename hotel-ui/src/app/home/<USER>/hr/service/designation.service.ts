import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';


@Injectable({
  providedIn: 'root'
})
export class DesignationService {

  constructor(private http: HttpClient) {
  }

  save(designation) {
    return this.http.post<any>(HrApiConstants.SAVE_DESIGNATION, designation);
  }

  public findAll() {
    return this.http.get(HrApiConstants.GET_DESIGNATION);
  }


  public findAllPagination(page, pageSize) {
    return this.http.get(HrApiConstants.GET_DESIGNATION, {params: {page: page, pageSize: pageSize}});
  }


  public remove(designation) {
    return this.http.post<any>(HrApiConstants.DELETE_DESIGNATION, designation);
  }

  public findAllByDesignation(search) {
    return this.http.get(HrApiConstants.FIND_ALL_BY_DESIGNATION_LIKE, {params: {name: search}});
  }

  public findByDesignation(designation) {
    return this.http.post<any>(HrApiConstants.FIND_BY_DESIGNATION, designation);
  }

  //  public findAllByDesignation (keyDesignation:string) {
  //    return this.http.get(ApiConstants.FIND_ALL_BY_DESIGNATION_LIKE, {params: {name: keyDesignation}});
  //  }
  getAll() {
    return this.http.get(HrApiConstants.FIND_DESIGNATION);
  }

  findById(id: string) {
    return this.http.get(HrApiConstants.FIND_BY_ID, {params: {id: id}});
  }


  findByDesignationName(name: string) {
    return this.http.get(HrApiConstants.FIND_BY_DESIGNATION_NAME, {params: {name: name}});
  }
}
