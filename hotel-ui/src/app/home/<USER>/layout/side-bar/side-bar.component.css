#side-bar {
  height: 100%;
  overflow-y: auto;
  position: relative;
  width: 50px; /* Reduced width for more compact sidebar */
}

#side-bar .badge {
  position: relative;
  right: 9px;
}

#side-bar i {
  font-size: 1.3rem; /* Slightly reduced icon size for compact sidebar */
  color: rgb(89, 12, 48); /* Theme color */
  padding: 8px 0; /* Reduced padding */
  display: block;
}

#side-bar .cursor-pointer:hover i {
  color: #333; /* Darker on hover */
  transform: scale(1.1); /* Slightly enlarge on hover */
  transition: all 0.2s ease;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  #side-bar {
    width: 45px; /* Even more compact on mobile */
  }
  
  #side-bar i {
    font-size: 1.1rem; /* Smaller icons on mobile */
    padding: 6px 0;
  }

  #side-bar .m-2 {
    margin: 0.2rem !important; /* Reduce margin on mobile */
  }
}

/* Very small screens */
@media (max-width: 480px) {
  #side-bar {
    width: 40px; /* Very compact on small screens */
  }
  
  #side-bar i {
    font-size: 1rem;
    padding: 5px 0;
  }
}
