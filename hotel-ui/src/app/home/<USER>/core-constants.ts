import {environment} from '../../../environments/environment';

export class CoreApiConstants {


  public static APP_VERSION = "25.06";

  public static API_URL = environment.apiUrl;

  public static LOGIN = CoreApiConstants.API_URL + 'login';

  // General Settings API endpoints
  public static SAVE_SETTING = CoreApiConstants.API_URL + 'settings/save';
  public static GET_ALL_SETTINGS = CoreApiConstants.API_URL + 'settings/findAll';
  public static GET_SETTINGS_BY_CATEGORY = CoreApiConstants.API_URL + 'settings/findByCategory';
  public static GET_SETTING_BY_KEY = CoreApiConstants.API_URL + 'settings/findByKey';
  public static DELETE_SETTING = CoreApiConstants.API_URL + 'settings/delete';

  public static GET_RELATED_ROUTES = CoreApiConstants.API_URL + 'common/findRelatedRoutes';

  public static FIND_AVAILABLE_PERMISSION = CoreApiConstants.API_URL + 'user/findAvailablePermissions';
  public static FIND_DESKTOP_PERMISSIONS = CoreApiConstants.API_URL + 'user/findDesktopPermissions';
  public static FIND_PERMS_FOR_MODULE = CoreApiConstants.API_URL + 'user/findPermissionsByModule';
  public static SAVE_DESKTOP_PERMS = CoreApiConstants.API_URL + 'user/saveDesktopPerms';
  public static GET_ENABLED_MODULES = CoreApiConstants.API_URL + 'user/getEnabledModules';
  public static GET_MODULE_BY_ID = CoreApiConstants.API_URL + 'user/getModule';
  public static GET_PERMISSION = CoreApiConstants.API_URL + 'user/getPermission';
  public static GET_PERMISSION_BY_NAME = CoreApiConstants.API_URL + 'user/getPermissionByName';

  public static SAVE_COMPANY = CoreApiConstants.API_URL + 'company/save';
  public static SAVE_COMPANY_LOGO = CoreApiConstants.API_URL + 'company/saveCompanyLogo';
  public static GET_COMPANY = CoreApiConstants.API_URL + 'company/findCompany';

  public static FIND_SEQUENCE = CoreApiConstants.API_URL + 'sequence/findSequenceByName';
  public static SAVE_SEQUENCE = CoreApiConstants.API_URL + 'sequence/save';
  public static SAVE_SEQUENCE_IF_UNAVAILABLE = CoreApiConstants.API_URL + 'sequence/saveIfUnavailable';
  public static GET_SEQUENCES = CoreApiConstants.API_URL + 'sequence/findAll';

  public static SAVE_METADATA = CoreApiConstants.API_URL + 'metaData/save';
  public static SEARCH_METADATA = CoreApiConstants.API_URL + 'metaData/findByCategory';
  public static SEARCH_METADATA_BY_CAT_VAL = CoreApiConstants.API_URL + 'metaData/findMetaDataByCatVal';
  public static SEARCH_METADATA_BY_CAT_VAL_BOOL = CoreApiConstants.API_URL + 'metaData/findMetaDataByCatValBool';
  public static SEARCH_METADATA_BY_ID = CoreApiConstants.API_URL + 'metaData/findById';

  public static GET_TRANSACTIONS_BY_RANGE_FILTER_AND_OPERATOR = CoreApiConstants.API_URL + 'transaction/findByRangeFilterAndOperator';
  public static GET_TRANSACTIONS_BY_DATE_RANGE_AND_OPERATOR = CoreApiConstants.API_URL + 'transaction/findByDateRangeAndOperator';
  public static GET_TRANSACTIONS_BY_REFERENCE = CoreApiConstants.API_URL + 'transaction/findByRef';

}
