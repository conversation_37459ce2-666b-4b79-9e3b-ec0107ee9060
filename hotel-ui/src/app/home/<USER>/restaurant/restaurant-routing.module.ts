import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {OrderComponent} from './component/order/order.component';
import {TablesComponent} from "./component/tables/tables.component";

import {RecipesComponent} from "./component/recipes/recipes.component";
import {PosComponent} from "./component/pos/pos.component";
import {SelectTableModalComponent} from "./component/select-table-modal/select-table-modal.component";
import {TicketComponent} from "./component/ticket/ticket.component";
import {CheckOutDoneComponent} from "./component/check-out-done/check-out-done.component";
import {DeliveryDetailsComponent} from "./component/delivery-details/delivery-details.component";
import {DeliveryLocationComponent} from "./component/delivery-location/delivery-location.component";
import {CurrentOrdersComponent} from "./component/current-orders/current-orders.component";

const routes: Routes = [
  {
    path: 'order',
    component: OrderComponent
  },
  {
    path: 'tables',
    component: TablesComponent
  },
  {
    path: 'recipes',
    component: RecipesComponent
  },
  {
    path: 'pos',
    component: PosComponent
  },
  {
    path: 'ticket',
    component: TicketComponent
  },
  {
    path: 'checkout-done',
    component: CheckOutDoneComponent
  },
  {
    path: 'delivery-details',
    component: DeliveryDetailsComponent
  },
  {
    path: 'delivery-location',
    component: DeliveryLocationComponent
  },
  {
    path: 'current-orders',
    component: CurrentOrdersComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RestaurantRoutingModule {
}

export const restaurantRouteParams = [OrderComponent, TablesComponent, RecipesComponent,
  PosComponent, SelectTableModalComponent, TicketComponent, CheckOutDoneComponent, DeliveryDetailsComponent, DeliveryLocationComponent, CurrentOrdersComponent];
