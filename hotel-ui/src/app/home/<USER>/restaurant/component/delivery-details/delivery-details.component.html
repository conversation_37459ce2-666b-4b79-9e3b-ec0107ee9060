<div class="container-fluid professional-container">
<div class="container">
  <div class="text-center mt-2">
    Delivery Charges
  </div>
  <div class="row m-2">
    <div class="col-md-12">
      <div class="input-group search-professional">
        <input type="text"
             class="form-control"
             [(ngModel)]="keyDestination"
             [typeahead]="destinations"
             (typeaheadLoading)="loadDestinations()"
             (typeaheadOnSelect)="setSelectedDestination($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadOptionField="itemName"
             placeholder="Search Delivery Charges"
             name="destination"
             autocomplete="off"
             size="16">
          <button class="btn btn-primary" type="button" >
            <i class="fas fa-search"></i>
          </button>
      </div>
            </div>
    <div class="mt-2 col-md-12" *ngIf="deliveryDetails">
      <div class="row">
        <label class="col-6">{{destination.locationName}}</label>
        <label class="col-3">{{destination.charge}}</label>
        <button class="btn btn-primary col-3" (click)="addDestination()" >Ok</button>
      </div>
    </div>
  </div>
</div>


</div>

