import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {DeliveryLocation} from "../../model/delivery-location";
import {DeliveryLocationService} from "../../service/delivery-location.service";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
standalone: false,
  selector: 'app-delivery-details',
  templateUrl: './delivery-details.component.html',
  styleUrls: ['./delivery-details.component.css']
})
export class DeliveryDetailsComponent implements OnInit {

  keyDestination: string;
  destination: DeliveryLocation;
  destinations: Array<DeliveryLocation>;
  modalRef: BsModalRef;
  deliveryDetails: boolean;

  @Output() passEntry: EventEmitter<any> = new EventEmitter();

  constructor(private deliveryLocationService: DeliveryLocationService) {
  }

  ngOnInit(): void {
    this.destination = new DeliveryLocation();
    this.deliveryDetails = false;
  }

  loadDestinations() {
    this.deliveryLocationService.findByLocationNameLike(this.keyDestination).subscribe((data: Array<DeliveryLocation>) => {
      this.destinations = data;
    });
  }

  setSelectedDestination(event) {
    this.destination = event.item;
    this.deliveryDetails = true;
  }

  addDestination() {
    this.passEntry.emit(this.destination);
    this.modalRef.hide();
  }
}
