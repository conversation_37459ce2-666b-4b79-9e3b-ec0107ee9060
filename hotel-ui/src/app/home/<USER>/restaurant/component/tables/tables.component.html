<div class="container-fluid mt-3">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">TABLES MANAGEMENT</h2>
  </div>

  <!-- Professional Filter Section -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
      <h6 class="mb-0 text-primary"><i class="fas fa-filter me-2"></i>Search & Filter Options</h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Search By Table Number</label>
          <div class="input-group">
            <input [(ngModel)]="keyTableNo"
                   [typeahead]="tables"
                   (typeaheadLoading)="loadTables()"
                   (typeaheadOnSelect)="setSelectedTable()"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="tableNo"
                   placeholder="Search tables..."
                   autocomplete="off"
                   class="form-control"
                   name="tableNo">
            <button class="btn btn-primary" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Filter by Status</label>
          <select class="form-control" [(ngModel)]="statusFilter" (ngModelChange)="applyFilters()">
            <option value="">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Filter by Availability</label>
          <select class="form-control" [(ngModel)]="availabilityFilter" (ngModelChange)="applyFilters()">
            <option value="">All Tables</option>
            <option value="available">Available Only</option>
            <option value="occupied">Occupied Only</option>
          </select>
        </div>
        <div class="col-12 col-lg-3">
          <div class="d-flex gap-2 flex-wrap">
            <button type="button" class="btn btn-primary flex-fill" (click)="applyFilters()">
              <i class="fa fa-filter me-1"></i>Apply Filters
            </button>
            <button type="button" class="btn btn-secondary flex-fill" (click)="clearFilters()">
              <i class="fa fa-refresh me-1"></i>Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-4">
    <!-- Tables List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Tables</h5>
            <small class="text-muted">{{ filteredTables?.length || 0 }} tables found</small>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-professional table-hover table-striped table-hover mb-0">
              <thead class="table-light">
              <tr>
                <th class="border-0 fw-semibold">
                  <i class="fas fa-hashtag me-2"></i>Table No
                </th>
                <th class="border-0 fw-semibold">
                  <i class="fas fa-sticky-note me-2"></i>Note
                </th>
                <th class="border-0 fw-semibold text-center">
                  <i class="fas fa-toggle-on me-2"></i>Status
                </th>
                <th class="border-0 fw-semibold text-center">
                  <i class="fas fa-chair me-2"></i>Availability
                </th>
                <th class="border-0 fw-semibold text-center">Actions</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let tbl of filteredTables; let i = index"
                  class="table-row"
                  [class.active]="i === selectedRow"
                  (click)="tableDetail(tbl, i)">
                <td class="align-middle">
                  <span class="table-number-badge">{{ tbl.tableNo }}</span>
                </td>
                <td class="align-middle">
                  <span class="text-muted">{{ tbl.note || 'No notes' }}</span>
                </td>
                <td class="align-middle text-center">
                    <span class="badge" [class]="tbl.active ? 'bg-success' : 'bg-danger'">
                      <i class="fas" [class]="tbl.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ tbl.active ? 'Active' : 'Inactive' }}
                    </span>
                </td>
                <td class="align-middle text-center">
                    <span class="badge" [class]="tbl.available ? 'bg-primary' : 'bg-warning'">
                      <i class="fas" [class]="tbl.available ? 'fa-chair' : 'fa-user-friends'"></i>
                      {{ tbl.available ? 'Available' : 'Occupied' }}
                    </span>
                </td>
                <td class="align-middle text-center">
                  <button class="btn btn-sm btn-primary"
                          (click)="tableDetail(tbl, i); $event.stopPropagation()">
                    <i class="fas fa-edit"></i>
                  </button>
                </td>
              </tr>
              <tr *ngIf="filteredTables?.length === 0">
                <td colspan="100%" class="text-center empty-state-professional">
                  <i class="fas fa-table fa-2x mb-3 d-block"></i>
                  No tables found
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination pagination-sm justify-content-center pagination-professional"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Table Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ table.id ? 'Edit Table' : 'Table Details' }}
          </h5>
        </div>

        <div class="card-body professional-card-body">
          <form #manageTableForm="ngForm" (ngSubmit)="saveTable()" class="form-professional">
            <!-- Table Number Field -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold">Table Number</label>
              <input type="text"
                     class="form-control"
                     [(ngModel)]="table.tableNo"
                     name="tableNo"
                     placeholder="Enter table number or leave blank for auto-generation"
                     [readonly]="false">
              <div class="form-text">Leave blank for auto-generation or enter custom table number</div>
            </div>

            <!-- Note Field -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold">Notes</label>
              <textarea class="form-control"
                        rows="3"
                        [(ngModel)]="table.note"
                        name="note"
                        placeholder="Add any notes about this table..."></textarea>
            </div>

            <!-- Status Toggles -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold mb-3">Table Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch mb-3">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="isActive"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this table for use</div>
                </div>

                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="availableSwitch"
                         [(ngModel)]="isAvailable"
                         name="available">
                  <label class="form-check-label fw-semibold" for="availableSwitch">
                    Available for Seating
                  </label>
                  <div class="form-text">Mark table as available for customers</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2 text-end">
              <button type="button" class="btn btn-primary btn-secondary ms-2" (click)="clear()">
                Clear
              </button>
              <button type="submit" class="btn btn-primary ms-2">
                {{ table.id ? 'Update Table' : 'Save Table' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

