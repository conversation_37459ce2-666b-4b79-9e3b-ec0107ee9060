<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <!-- Header -->
  <div class="theme-color">
    <div class="d-flex justify-content-between align-items-center header-section">
      <h3 class="mb-0">Viganana POS</h3>

      <!-- Desktop Action Icons -->
      <div class="d-none d-md-flex align-items-center">
        <button class="btn btn-light btn-icon me-2" (click)="showTables()" title="Select Table">
          <i class="fa fa-table"></i>
        </button>
        <button class="btn btn-light btn-icon me-2" (click)="openViewAllItems()" title="Items">
          <i class="fa fa-list"></i>
        </button>
        <button class="btn btn-light btn-icon me-2" (click)="openPastInvoice()" title="Sales History">
          <i class="fa fa-history"></i>
        </button>
        <button class="btn btn-light btn-icon me-2" (click)="openCashier()" title="Cash Drawer">
          <i class="fa fa-cash-register"></i>
        </button>
        <button class="btn btn-light btn-icon" routerLink="../home/<USER>" (click)="closeFullscreen()" title="Home">
          <i class="fa fa-home"></i>
        </button>
      </div>
    </div>

    <!-- Mobile Action Icons -->
    <div class="d-flex justify-content-around d-md-none mt-2">
      <button class="btn btn-sm btn-secondary" (click)="showTables()"><i
        class="fa fa-table"></i></button>
      <button class="btn btn-sm btn-secondary" (click)="showCategories()"><i class="fa fa-list"></i>
      </button>
      <button class="btn btn-sm btn-secondary" (click)="openPastInvoice()"><i
        class="fa fa-history"></i></button>
      <button class="btn btn-sm btn-secondary" (click)="openCashier()"><i
        class="fa fa-cash-register"></i></button>
      <button class="btn btn-sm btn-secondary" routerLink="../home/<USER>" (click)="closeFullscreen()"
      ><i class="fa fa-home"></i></button>
    </div>
  </div>

  <!-- Content -->
  <div class="content-section">
    <div class="row g-0 h-100">
      <!-- Orders Sidebar -->
      <div class="col-md-2 col-lg-1 border-end bg-white d-flex flex-column">
        <h5 class="text-primary mb-1 small-screen-title">Orders</h5>
        <ul class="list-group list-group-flush overflow-auto flex-grow-1">
          <li class="list-group-item list-group-item-action"
              *ngFor="let ordr of waitingOrders; let i = index"
              (click)="selectRecord(ordr, i)"
              [class.active]="i === selectedRow">
            <div class="d-flex justify-content-between align-items-center">
              <div class="small-text">
                <span>{{ ordr.orderNo }}</span>
                <small class="text-muted d-block">{{ ordr.tableNo }}</small>
                <small class="badge"
                       [class.badge-success]="ordr.status === 'Active'"
                       [class.badge-warning]="ordr.status === 'Completed'"
                       [class.badge-danger]="ordr.status === 'Cancelled'">
                  {{ ordr.status }}
                </small>
              </div>
              <div class="btn-group-vertical" *ngIf="i === selectedRow && ordr.status === 'Active'">
                <button class="btn btn-danger btn-sm btn-xs"
                        (click)="cancelOrder(ordr, $event)"
                        title="Cancel Order">
                  <i class="fa fa-times"></i>
                </button>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Main Section -->
      <div class="col-md-7 col-lg-8 main-section">
        <!-- Search Bar -->
        <div class="row g-1 mb-2">
          <div class="col-md-5 col-lg-4">
            <input #barcodeElm class="form-control form-control-sm"
                   [(ngModel)]="keyBarcodeSearch"
                   [typeahead]="itemSearchList"
                   (typeaheadLoading)="searchItemsByBarcode()"
                   (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="barcode"
                   placeholder="Scan barcode"
                   name="searchItem">
          </div>
          <div class="col-md-5 col-lg-4">
            <input #itemName class="form-control form-control-sm"
                   [(ngModel)]="keyItemNameSearch"
                   [typeahead]="itemNameSearchList"
                   (typeaheadLoading)="searchItemsByName()"
                   (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                   [typeaheadOptionsLimit]="10"
                   [typeaheadItemTemplate]="customItemTemplate"
                   typeaheadOptionField="itemName"
                   placeholder="Search item"
                   name="searchItem">
            <ng-template #customItemTemplate let-model="item">
              <span><strong>{{ model.itemName }}</strong> - Rs {{ model.sellingPrice }}</span>
            </ng-template>
          </div>
          <div class="col-md-1 col-lg-2">
            <input #sellingPrice type="number" class="form-control form-control-sm" placeholder="Price"
                   [(ngModel)]="sPrice">
          </div>
          <div class="col-md-1 col-lg-2">
            <input #quantity type="number" class="form-control form-control-sm" placeholder="Qty"
                   [(ngModel)]="itemQty" (keydown.enter)="addToInvoice()">
          </div>
        </div>

        <!-- Main Dynamic Content -->
        <div class="main-content">
          <!-- Category View -->
          <div class="row g-1" *ngIf="isCategory">
            <div class="col-md-4 col-lg-3" *ngFor="let category of categories">
              <div class="category-card cursor-pointer" (click)="selectCategory(category)">
                <div class="category-text">{{ category.categoryName }}</div>
              </div>
            </div>
          </div>

          <!-- Subcategory View -->
          <div class="row g-1" *ngIf="isSubCategory">
            <div class="col-md-4 col-lg-3" *ngFor="let subCategory of subCategories">
              <div class="subcategory-card cursor-pointer" (click)="selectSubCategory(subCategory)">
                <div class="subcategory-text">{{ subCategory.subCategoryName }}</div>
              </div>
            </div>
          </div>

          <!-- Item View -->
          <div class="row g-1" *ngIf="!isCategory && !isSubCategory">
            <div class="col-md-4 col-lg-3 mb-2" *ngFor="let item of items">
              <div class="card text-center cursor-pointer item-card" (click)="setItem(item)">
                <div class="item-name">{{ item.itemName }}  <i class="fa fa-arrow-alt-circle-right fa-sm ms-1"></i></div>
                <small class="text-muted">Rs: {{ item.sellingPrice | number:'1.2-2' }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Fixed Navigation Buttons -->
        <div class="fixed-navigation-buttons">
          <div class="d-flex justify-content-center gap-2 mb-2 mt-1">
            <button class="btn btn-sm" (click)="showCategories()"
                    [class.btn-primary]="isCategory" [class.btn-outline-primary]="!isCategory">
              <i class="fa fa-list me-1"></i>Categories
            </button>
            <button class="btn btn-sm" (click)="showSubCategories()"
                    [class.btn-primary]="isSubCategory" [class.btn-outline-primary]="!isSubCategory"
                    [disabled]="!selectedCategory">
              <i class="fa fa-folder-open me-1"></i>Subcategories
            </button>
          </div>
        </div>

        <!-- Contextual navigation buttons removed as requested -->
      </div>

      <!-- Invoice Section -->
      <div class="col-md-3 col-lg-3 bg-light border-start d-flex flex-column">
        <!-- Clear Button and Table Selection -->
        <div class="d-flex gap-1 mb-2 p-1">
          <button class="btn btn-sm btn-secondary" (click)="clearAll()"
                  [disabled]="!(invoice?.salesInvoiceRecords?.length > 0)">
            <i class="fa fa-trash me-1"></i>Clear
          </button>
          <div class="flex-grow-1" [ngClass]="selectedTableNo ? 'table-selection' : 'table-not-selected'">
            <div class="d-flex justify-content-between align-items-center">
              <div class="small-text"><i class="fa fa-table me-1"></i> <strong>{{ selectedTableNo || 'Select Table' }}</strong></div>
              <button class="btn btn-xs btn-light" (click)="openTableSelection()"><i
                class="fa fa-edit"></i></button>
            </div>
          </div>
        </div>

        <!-- Invoice Items -->
        <div class="flex-grow-1 overflow-auto px-2">
          <div *ngFor="let rec of invoice?.salesInvoiceRecords; let i = index" class="invoice-item">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <div class="flex-grow-1 invoice-item-text">
                <div class="fw-bold">{{ rec.itemName }}</div>
                <small class="text-muted d-none d-lg-inline">{{ rec.barcode || 'No barcode' }}</small>
              </div>
              <button class="btn btn-xs btn-danger" (click)="remove(i)">
                <i class="fa fa-trash"></i>
              </button>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <button class="btn btn-xs btn-danger me-1"
                        (click)="decreaseQuantity(i, 'displayQuantity', 'quantity', rec)">
                  <i class="fa fa-minus"></i>
                </button>
                <span class="px-1 fw-bold">{{ rec.displayQuantity }}</span>
                <button class="btn btn-xs btn-outline-success"
                        (click)="increaseQuantity(i, 'displayQuantity', 'quantity', rec)">
                  <i class="fa fa-plus"></i>
                </button>
              </div>
              <div class="text-end">
                <div class="fw-bold text-primary small-price">Rs {{ rec.price | number:'1.2-2' }}</div>
                <small *ngIf="rec.ticketType" class="text-muted d-none d-lg-inline">
                  <i class="fa fa-tag me-1"></i>{{ rec.ticketType }}
                </small>
              </div>
            </div>
          </div>
          <div *ngIf="invoice?.salesInvoiceRecords?.length === 0" class="text-center text-muted py-4">
            <i class="fa fa-shopping-cart fa-3x mb-3"></i>
            <p>No items added yet</p>
            <small>Select items from categories or search by name/barcode</small>
          </div>
        </div>

        <!-- Invoice Summary -->
        <div class="mt-3 px-3">
          <div class="summary-row">
            <span>Sub Total</span>
            <strong>{{ invoice.subTotal | number:'1.2-2' }}</strong>
          </div>
          <div class="summary-row">
            <span>Service Charge</span>
            <input type="number" class="form-control" [(ngModel)]="invoice.serviceCharge"
                   (ngModelChange)="calculateTotal()" style="width: 180px; text-align: right; font-size: 1rem;"
                   placeholder="0.00" step="0.01">
          </div>
          <hr class="my-2">
          <div class="summary-row total-row">
            <strong>Total</strong>
            <strong>{{ invoice.totalAmount | number:'1.2-2' }}</strong>
          </div>
          <div class="summary-row">
            <span>Payment</span>
            <input #payment type="number" class="form-control" [(ngModel)]="invoice.payment"
                   (ngModelChange)="calculateBalance()" style="width: 180px; text-align: right; font-size: 1rem;"
                   placeholder="0.00" step="0.01">
          </div>
          <div class="summary-row">
            <span>Balance</span>
            <strong>{{ invoice.cashBalance | number:'1.2-2' }}</strong>
          </div>

          <!-- Payment Type -->
          <div class="d-flex justify-content-center mb-2">
            <div class="form-check form-check-inline me-2">
              <input class="form-check-input" type="radio" name="paymentMethod" id="cash" value="Cash"
                     [(ngModel)]="paymentMethodName">
              <label class="form-check-label small-text" for="cash">Cash</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="paymentMethod" id="card" value="Card"
                     [(ngModel)]="paymentMethodName">
              <label class="form-check-label small-text" for="card">Card</label>
            </div>
          </div>

          <!-- Actions -->
          <div class="d-flex flex-wrap gap-1 justify-content-evenly">
            <button class="btn btn-sm btn-primary action-btn" (click)="openModalSelectTable()"
                    [disabled]="!(invoice?.salesInvoiceRecords?.length > 0)">
              <i class="fa fa-plus-circle me-1"></i>Order
            </button>
            <button class="btn btn-sm btn-info action-btn" (click)="printFullKOTBOT()"
                    [disabled]="!(invoice?.salesInvoiceRecords?.length > 0)">
              <i class="fa fa-print me-1"></i>Ticket
            </button>
            <button class="btn btn-sm btn-success action-btn" (click)="save(true)"
                    [disabled]="!(invoice?.salesInvoiceRecords?.length > 0) || !ableToSaveInvoice">
              <i class="fa fa-check-circle me-1"></i>Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Employee Selection Modal -->
  <div class="modal fade" id="employeeModal" tabindex="-1" aria-labelledby="employeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="employeeModalLabel">Select Person in Charge</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Search Employee</label>
            <input type="text" class="form-control"
                   [(ngModel)]="keyEmployeeSearch"
                   [typeahead]="employeeSearchList"
                   (typeaheadLoading)="searchEmployeesByName()"
                   (typeaheadOnSelect)="setSelectedEmployee($event)"
                   typeaheadOptionField="name"
                   placeholder="Type employee name to search..."
                   autocomplete="off">
          </div>

          <div *ngIf="selectedEmployee" class="alert alert-info">
            <strong>Selected:</strong> {{ selectedEmployee.name }}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="clearSelectedEmployee()">Clear</button>
          <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>
