import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Recipe } from '../../model/recipe';
import { RecipeIngredient } from '../../model/recipe-ingredient';
import { Item } from '../../../inventory/model/item';
import { ItemType } from '../../../inventory/model/item-type';
import { RecipeService } from '../../service/recipe.service';
import { ItemService } from '../../../inventory/service/item.service';
import { ItemTypeService } from '../../../inventory/service/item-type.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
standalone: false,
  selector: 'app-recipes',
  templateUrl: './recipes.component.html',
  styleUrls: ['./recipes.component.css']
})
export class RecipesComponent implements OnInit {

  @ViewChild('recipeModal', {static: false}) recipeModal: TemplateRef<any>;

  // Modal reference
  modalRef: BsModalRef;

  // Recipe management
  recipe: Recipe = new Recipe();
  recipes: Recipe[] = [];
  isEditing: boolean = false;
  isModal: boolean = false;

  // Food items (for recipe selection)
  foodItems: Item[] = [];
  selectedFoodItem: Item;
  keyFoodSearch: string = '';

  // Ingredient management
  ingredients: Item[] = [];
  selectedIngredient: Item;
  keyIngredientSearch: string = '';
  ingredientQuantity: number = 0;

  // Search and pagination
  keyRecipeSearch: string = '';
  currentPage: number = 1;
  pageSize: number = 10;

  constructor(
    private recipeService: RecipeService,
    private itemService: ItemService,
    private modalService: BsModalService,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.loadRecipes();
  }

  // Load all recipes
  loadRecipes(): void {
    this.recipeService.findAllActive().subscribe(
      (data: Recipe[]) => {
        this.recipes = data || [];
      },
      (error) => {
        console.error('Error loading recipes:', error);
        this.notificationService.showError('Failed to load recipes');
      }
    );
  }

  // Load food items (items with type "Food")
  loadFoodItems(): void {
    this.itemService.findFoods(this.keyFoodSearch).subscribe(
      (data: Item[]) => {
        this.foodItems = data || [];
      },
      (error) => {
        console.error('Error loading food items:', error);
        this.notificationService.showError('Failed to load food items');
      }
    );
  }

  // Load ingredients (items with type "Ingredient")
  loadIngredients(): void {
    this.itemService.findIngredients(this.keyIngredientSearch).subscribe(
      (data: Item[]) => {
        this.ingredients = data || [];
      },
      (error) => {
        console.error('Error loading ingredients:', error);
        this.notificationService.showError('Failed to load ingredients');
      }
    );
  }

  // Open modal for new recipe
  openNewRecipeModal(): void {
    this.recipe = new Recipe();
    this.selectedFoodItem = null;
    this.keyFoodSearch = '';

    // Clear ingredient form fields
    this.selectedIngredient = null;
    this.keyIngredientSearch = '';
    this.ingredientQuantity = 0;

    this.isEditing = false;
    this.modalRef = this.modalService.show(this.recipeModal, {class: 'modal-lg'});
  }

  // Open modal for editing recipe
  editRecipe(recipe: Recipe): void {
    // Deep copy the recipe to avoid modifying the original
    this.recipe = {
      ...recipe,
      ingredients: recipe.ingredients ? [...recipe.ingredients] : []
    };

    // Set food item selection
    this.selectedFoodItem = recipe.foodItem;
    this.keyFoodSearch = recipe.foodItem ? recipe.foodItem.itemName : '';

    // Clear ingredient form fields
    this.selectedIngredient = null;
    this.keyIngredientSearch = '';
    this.ingredientQuantity = 0;
  }

  // Save recipe
  saveRecipe(): void {
    if (!this.selectedFoodItem) {
      this.notificationService.showError('Please select a food item');
      return;
    }

    if (!this.recipe.recipeName || this.recipe.recipeName.trim() === '') {
      this.notificationService.showError('Please enter recipe name');
      return;
    }

    this.recipe.foodItem = this.selectedFoodItem;

    this.recipeService.save(this.recipe).subscribe(
      (response: any) => {
        if (response.code === 200) {
          this.notificationService.showSuccess('Recipe saved successfully');
          this.loadRecipes();
          this.clearForm();
          if (this.isModal) {
            this.closeModal();
          }
        } else {
          this.notificationService.showError('Failed to save recipe: ' + response.message);
        }
      },
      (error) => {
        console.error('Error saving recipe:', error);
        this.notificationService.showError('Failed to save recipe');
      }
    );
  }

  // Add ingredient to recipe
  addIngredient(): void {
    if (!this.selectedIngredient) {
      this.notificationService.showError('Please select an ingredient');
      return;
    }

    if (this.ingredientQuantity <= 0) {
      this.notificationService.showError('Please enter a valid quantity in grams');
      return;
    }

    // Check if ingredient already exists in recipe
    const existingIngredient = this.recipe.ingredients.find(
      ing => ing.ingredient.id === this.selectedIngredient.id
    );

    if (existingIngredient) {
      this.notificationService.showError('This ingredient is already added to the recipe');
      return;
    }

    // Add new ingredient
    const recipeIngredient = new RecipeIngredient(
      this.selectedIngredient,
      this.ingredientQuantity,
      '' // No notes field anymore
    );

    this.recipe.ingredients.push(recipeIngredient);

    // Clear form
    this.selectedIngredient = null;
    this.ingredientQuantity = 0;
    this.keyIngredientSearch = '';
  }

  // Remove ingredient from recipe
  removeIngredient(index: number): void {
    this.recipe.ingredients.splice(index, 1);
  }

  // Update ingredient quantity
  updateIngredientQuantity(index: number, quantity: number): void {
    if (quantity > 0) {
      this.recipe.ingredients[index].quantityInGrams = quantity;
    }
  }

  // Search recipes
  searchRecipes(): void {
    if (this.keyRecipeSearch && this.keyRecipeSearch.trim() !== '') {
      this.recipeService.searchByName(this.keyRecipeSearch).subscribe(
        (data: Recipe[]) => {
          this.recipes = data || [];
        },
        (error) => {
          console.error('Error searching recipes:', error);
          this.notificationService.showError('Failed to search recipes');
        }
      );
    } else {
      this.loadRecipes();
    }
  }

  // Delete recipe
  deleteRecipe(recipe: Recipe): void {
    if (confirm('Are you sure you want to delete this recipe?')) {
      this.recipeService.delete(recipe.id).subscribe(
        (response: any) => {
          this.notificationService.showSuccess('Recipe deleted successfully');
          this.loadRecipes();
        },
        (error) => {
          console.error('Error deleting recipe:', error);
          this.notificationService.showError('Failed to delete recipe');
        }
      );
    }
  }

  // Close modal
  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Clear the recipe form
   */
  clearForm(): void {
    this.recipe = new Recipe();
    this.selectedFoodItem = null;
    this.keyFoodSearch = '';
    this.selectedIngredient = null;
    this.keyIngredientSearch = '';
    this.ingredientQuantity = 0;
    this.isEditing = false;
  }



  // Set selected food item from typeahead
  setSelectedFoodItem(event: any): void {
    this.selectedFoodItem = event.item;
  }

  // Set selected ingredient from typeahead
  setSelectedIngredient(event: any): void {
    this.selectedIngredient = event.item;
  }

  // Calculate total ingredients count
  getTotalIngredientsCount(): number {
    return this.recipe.ingredients ? this.recipe.ingredients.length : 0;
  }

  // Calculate total weight of ingredients
  getTotalWeight(): number {
    if (!this.recipe.ingredients) return 0;
    return this.recipe.ingredients.reduce((total, ing) => total + ing.quantityInGrams, 0);
  }
}
