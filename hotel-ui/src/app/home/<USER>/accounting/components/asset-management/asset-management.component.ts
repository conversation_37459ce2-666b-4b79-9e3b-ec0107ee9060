import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Asset } from '../../model/asset';
import { AssetService } from '../../service/asset.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-asset-management',
  templateUrl: './asset-management.component.html',
  styleUrls: ['./asset-management.component.css']
})
export class AssetManagementComponent implements OnInit {

  asset = new Asset();
  assets: Array<Asset> = [];
  selectedAsset: Asset = null;

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Filters
  typeFilter = 'ALL';
  statusFilter = 'ALL';
  categoryFilter = 'ALL';

  // Options
  assetTypes = [
    { value: 'ALL', label: 'All Types' },
    { value: 'CURRENT', label: 'Current Assets' },
    { value: 'FIXED', label: 'Fixed Assets' }
  ];

  statusOptions = [
    { value: 'ALL', label: 'All Status' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'DISPOSED', label: 'Disposed' },
    { value: 'UNDER_MAINTENANCE', label: 'Under Maintenance' }
  ];

  categories = [
    'Cash', 'Bank', 'Equipment', 'Furniture', 'Building', 'Vehicle', 'Computer', 'Other'
  ];

  depreciationMethods = [
    { value: 'STRAIGHT_LINE', label: 'Straight Line' },
    { value: 'DECLINING_BALANCE', label: 'Declining Balance' }
  ];

  constructor(
    private assetService: AssetService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAssets();
  }

  initializeForm(): void {
    this.asset = new Asset();
    this.asset.purchaseDate = new Date();
  }

  save(form: NgForm): void {
    if (!this.asset.assetName || this.asset.assetName.trim() === '') {
      this.notificationService.showError('Please enter asset name');
      return;
    }

    if (!this.asset.purchaseValue || this.asset.purchaseValue <= 0) {
      this.notificationService.showError('Please enter a valid purchase value');
      return;
    }

    this.assetService.save(this.asset).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Asset saved successfully', 'Failed to save asset');
        form.reset();
        this.initializeForm();
        this.loadAssets();
      },
      (error) => {
        this.notificationService.showError('Failed to save asset: ' + (error.message || 'Unknown error'));
      }
    );
  }

  loadAssets(): void {
    this.assetService.findAll(this.page - 1, this.pageSize).subscribe(
      (data: any) => {
        this.assets = data.content || data;
        this.collectionSize = data.totalElements || this.assets.length;
        this.applyFilters();
      },
      (error) => {
        console.error('Error loading assets:', error);
        this.assets = [];
      }
    );
  }

  selectAsset(asset: Asset, index: number): void {
    this.selectedAsset = asset;
  }

  editAsset(): void {
    if (!this.selectedAsset) {
      this.notificationService.showError('Please select an asset to edit');
      return;
    }
    this.asset = { ...this.selectedAsset };
  }

  calculateDepreciation(): void {
    if (!this.selectedAsset) {
      this.notificationService.showError('Please select an asset to calculate depreciation');
      return;
    }

    this.assetService.calculateDepreciation(this.selectedAsset.id).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Depreciation calculated successfully', 'Failed to calculate depreciation');
        this.loadAssets();
      },
      (error) => {
        this.notificationService.showError('Failed to calculate depreciation: ' + (error.message || 'Unknown error'));
      }
    );
  }

  disposeAsset(): void {
    if (!this.selectedAsset) {
      this.notificationService.showError('Please select an asset to dispose');
      return;
    }

    const disposalValue = prompt('Enter disposal value:');
    const reason = prompt('Enter disposal reason:');

    if (disposalValue && reason) {
      this.assetService.disposeAsset(
        this.selectedAsset.id,
        new Date(),
        parseFloat(disposalValue),
        reason
      ).subscribe(
        (result) => {
          this.notificationService.handleResponse(result, 'Asset disposed successfully', 'Failed to dispose asset');
          this.loadAssets();
          this.selectedAsset = null;
        },
        (error) => {
          this.notificationService.showError('Failed to dispose asset: ' + (error.message || 'Unknown error'));
        }
      );
    }
  }

  applyFilters(): void {
    let filteredAssets = [...this.assets];

    if (this.typeFilter !== 'ALL') {
      filteredAssets = filteredAssets.filter(asset => asset.assetType === this.typeFilter);
    }

    if (this.statusFilter !== 'ALL') {
      filteredAssets = filteredAssets.filter(asset => asset.status === this.statusFilter);
    }

    if (this.categoryFilter !== 'ALL') {
      filteredAssets = filteredAssets.filter(asset => asset.category === this.categoryFilter);
    }

    this.assets = filteredAssets;
    this.collectionSize = this.assets.length;
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.typeFilter = 'ALL';
    this.statusFilter = 'ALL';
    this.categoryFilter = 'ALL';
    this.loadAssets();
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadAssets();
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'ACTIVE': return 'badge-success';
      case 'DISPOSED': return 'badge-danger';
      case 'UNDER_MAINTENANCE': return 'badge-warning';
      default: return 'badge-secondary';
    }
  }

  getTypeClass(type: string): string {
    switch (type) {
      case 'CURRENT': return 'badge-info';
      case 'FIXED': return 'badge-primary';
      default: return 'badge-secondary';
    }
  }

  calculateAge(purchaseDate: Date): string {
    if (!purchaseDate) return 'N/A';

    const now = new Date();
    const purchase = new Date(purchaseDate);
    const diffTime = Math.abs(now.getTime() - purchase.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);

    if (years > 0) {
      return `${years}y ${months}m`;
    } else {
      return `${months}m`;
    }
  }

  clear(): void {
    this.initializeForm();
  }
}
