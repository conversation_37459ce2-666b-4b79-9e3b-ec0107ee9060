import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BankAccount } from '../../../trade/model/bank-account';
import { BankAccountTransaction } from '../../../trade/model/bank-account-transaction';
import { BankAccountService } from '../../service/bank-account.service';
import { PaymentConstants } from '../../../trade/constants/payment-constants';
import { NotificationService } from '../../../../core/service/notification.service';
import { Response } from '../../../../core/model/response';

@Component({
  standalone: false,
  selector: 'app-bank-account-management',
  templateUrl: './bank-account-management.component.html',
  styleUrls: ['./bank-account-management.component.css']
})
export class BankAccountManagementComponent implements OnInit {

  @ViewChild('bankAccountModal', { static: false }) bankAccountModal: TemplateRef<any>;
  @ViewChild('transactionModal', { static: false }) transactionModal: TemplateRef<any>;
  @ViewChild('balanceCorrectionModal', { static: false }) balanceCorrectionModal: TemplateRef<any>;

  // Data arrays
  bankAccountList: BankAccount[] = [];
  transactionList: BankAccountTransaction[] = [];
  accountTypes: Array<{value: string, label: string}> = [];

  // Form objects
  bankAccount: BankAccount = new BankAccount();
  transaction: BankAccountTransaction = new BankAccountTransaction();

  // Modal references
  modalRef: BsModalRef;

  // UI state
  isEditMode = false;
  selectedBankAccount: BankAccount = null;
  loading = false;

  // Transaction form fields
  transactionAmount: number = 0;
  transactionDescription: string = '';
  referenceNo: string = '';
  thirdParty: string = '';
  selectedTransactionType: string = PaymentConstants.TRANSACTION_TYPE_CREDIT;

  // Balance correction fields
  correctionAmount: number = 0;
  correctionReason: string = '';
  selectedBankAccountForCorrection: BankAccount = null;

  // Search and filter
  searchTerm: string = '';
  selectedAccountType: string = '';

  constructor(
    private bankAccountService: BankAccountService,
    private modalService: BsModalService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadBankAccountList();
    this.loadAccountTypes();
    this.resetForm();
  }

  // Load data methods
  loadBankAccountList(): void {
    this.loading = true;
    this.bankAccountService.findAllBankAccounts().subscribe(
      (data: BankAccount[]) => {
        this.bankAccountList = data || [];
        this.loading = false;
      },
      (error) => {
        console.error('Error loading bank account list:', error);
        this.notificationService.showError('Error loading bank accounts');
        this.loading = false;
      }
    );
  }

  loadAccountTypes(): void {
    this.accountTypes = PaymentConstants.getAccountTypes();
  }

  loadTransactions(bankAccountId: string): void {
    this.bankAccountService.findTransactionsByBankAccount(bankAccountId).subscribe(
      (data: BankAccountTransaction[]) => {
        this.transactionList = data || [];
      },
      (error) => {
        console.error('Error loading transactions:', error);
        this.notificationService.showError('Error loading transactions');
      }
    );
  }

  // Modal methods
  openBankAccountModal(): void {
    this.resetForm();
    this.isEditMode = false;
    this.modalRef = this.modalService.show(this.bankAccountModal, { class: 'modal-lg' });
  }

  openEditModal(bankAccount: BankAccount): void {
    this.bankAccount = { ...bankAccount };
    this.isEditMode = true;
    this.modalRef = this.modalService.show(this.bankAccountModal, { class: 'modal-lg' });
  }

  openTransactionModal(bankAccount: BankAccount): void {
    this.selectedBankAccount = bankAccount;
    this.loadTransactions(bankAccount.id);
    this.resetTransactionForm();
    this.modalRef = this.modalService.show(this.transactionModal, { class: 'modal-xl' });
  }

  openBalanceCorrectionModal(bankAccount: BankAccount): void {
    this.selectedBankAccountForCorrection = bankAccount;
    this.correctionAmount = bankAccount.currentBalance;
    this.correctionReason = '';
    this.modalRef = this.modalService.show(this.balanceCorrectionModal, { class: 'modal-lg' });
  }

  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.resetForm();
    this.resetTransactionForm();
    this.resetBalanceCorrectionForm();
  }

  // Form methods
  resetForm(): void {
    this.bankAccount = new BankAccount();
    this.bankAccount.active = true;
    this.bankAccount.accountType = PaymentConstants.ACCOUNT_TYPE_CURRENT;
    this.bankAccount.currentBalance = 0;
    this.bankAccount.openingBalance = 0;
    this.isEditMode = false;
  }

  resetTransactionForm(): void {
    this.transaction = new BankAccountTransaction();
    this.transactionAmount = 0;
    this.transactionDescription = '';
    this.referenceNo = '';
    this.thirdParty = '';
    this.selectedTransactionType = PaymentConstants.TRANSACTION_TYPE_CREDIT;
  }

  resetBalanceCorrectionForm(): void {
    this.correctionAmount = 0;
    this.correctionReason = '';
    this.selectedBankAccountForCorrection = null;
  }

  // CRUD operations
  saveBankAccount(): void {
    if (!this.validateBankAccountForm()) {
      return;
    }

    this.loading = true;
    this.bankAccountService.save(this.bankAccount).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadBankAccountList();
          this.closeModal();
        } else {
          this.notificationService.showError(response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error saving bank account:', error);
        this.notificationService.showError('Error saving bank account');
        this.loading = false;
      }
    );
  }

  deleteBankAccount(bankAccount: BankAccount): void {
    if (confirm('Are you sure you want to delete this bank account?')) {
      bankAccount.active = false;
      this.bankAccountService.save(bankAccount).subscribe(
        (response: Response) => {
          if (response.success) {
            this.notificationService.showSuccess('Bank account deleted successfully');
            this.loadBankAccountList();
          } else {
            this.notificationService.showError(response.message);
          }
        },
        (error) => {
          console.error('Error deleting bank account:', error);
          this.notificationService.showError('Error deleting bank account');
        }
      );
    }
  }

  // Transaction operations
  createTransaction(): void {
    if (!this.validateTransactionForm()) {
      return;
    }

    this.transaction.bankAccount = this.selectedBankAccount;
    this.transaction.transactionType = this.selectedTransactionType;
    this.transaction.amount = this.transactionAmount;
    this.transaction.description = this.transactionDescription;
    this.transaction.referenceNo = this.referenceNo;
    this.transaction.thirdParty = this.thirdParty;
    this.transaction.paymentType = PaymentConstants.BANK_TRANSACTION_DEPOSIT;
    this.transaction.referenceType = PaymentConstants.REFERENCE_TYPE_DEPOSIT;

    this.bankAccountService.createTransaction(this.transaction).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadTransactions(this.selectedBankAccount.id);
          this.loadBankAccountList(); // Refresh to update balances
          this.resetTransactionForm();
        } else {
          this.notificationService.showError(response.message);
        }
      },
      (error) => {
        console.error('Error creating transaction:', error);
        this.notificationService.showError('Error creating transaction');
      }
    );
  }

  // Validation methods
  validateBankAccountForm(): boolean {
    if (!this.bankAccount.bankName || this.bankAccount.bankName.trim() === '') {
      this.notificationService.showError('Bank name is required');
      return false;
    }
    if (!this.bankAccount.accountNo || this.bankAccount.accountNo.trim() === '') {
      this.notificationService.showError('Account number is required');
      return false;
    }
    if (!this.bankAccount.accountHolderName || this.bankAccount.accountHolderName.trim() === '') {
      this.notificationService.showError('Account holder name is required');
      return false;
    }
    if (!this.bankAccount.accountType) {
      this.notificationService.showError('Account type is required');
      return false;
    }
    return true;
  }

  validateTransactionForm(): boolean {
    if (!this.transactionAmount || this.transactionAmount <= 0) {
      this.notificationService.showError('Transaction amount must be greater than 0');
      return false;
    }
    if (!this.transactionDescription || this.transactionDescription.trim() === '') {
      this.notificationService.showError('Transaction description is required');
      return false;
    }
    if (!this.selectedTransactionType) {
      this.notificationService.showError('Transaction type is required');
      return false;
    }
    return true;
  }

  // Utility methods
  getCurrentBalance(bankAccount: BankAccount): number {
    return bankAccount.currentBalance || 0;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LKR',
      minimumFractionDigits: 2
    }).format(amount || 0);
  }

  getAccountTypeLabel(accountType: string): string {
    const type = this.accountTypes.find(t => t.value === accountType);
    return type ? type.label : accountType;
  }

  getTransactionTypeClass(transactionType: string): string {
    return transactionType === PaymentConstants.TRANSACTION_TYPE_CREDIT ? 'text-success' : 'text-danger';
  }

  // Search and filter methods
  get filteredBankAccounts(): BankAccount[] {
    let filtered = this.bankAccountList;

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(account =>
        account.bankName?.toLowerCase().includes(term) ||
        account.accountNo?.toLowerCase().includes(term) ||
        account.accountHolderName?.toLowerCase().includes(term)
      );
    }

    if (this.selectedAccountType) {
      filtered = filtered.filter(account => account.accountType === this.selectedAccountType);
    }

    return filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedAccountType = '';
  }

  // Balance correction
  correctBalance(): void {
    if (!this.validateBalanceCorrectionForm()) {
      return;
    }

    this.loading = true;
    this.bankAccountService.correctBalance(
      this.selectedBankAccountForCorrection.id,
      this.correctionAmount,
      this.correctionReason,
      'Admin' // TODO: Get current user
    ).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadBankAccountList();
          this.closeModal();
        } else {
          this.notificationService.showError(response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error correcting balance:', error);
        this.notificationService.showError('Error correcting balance');
        this.loading = false;
      }
    );
  }

  validateBalanceCorrectionForm(): boolean {
    if (!this.correctionAmount || this.correctionAmount < 0) {
      this.notificationService.showError('Please enter a valid balance amount');
      return false;
    }
    if (!this.correctionReason || this.correctionReason.trim() === '') {
      this.notificationService.showError('Please provide a reason for balance correction');
      return false;
    }
    return true;
  }
}
