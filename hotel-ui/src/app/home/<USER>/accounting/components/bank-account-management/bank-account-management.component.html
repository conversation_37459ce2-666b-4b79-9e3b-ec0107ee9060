<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Bank Account Management</h2>

      <!-- Search and Filter Section -->
      <div class="card mb-4">
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>Search</label>
                <input type="text" class="form-control" [(ngModel)]="searchTerm"
                       placeholder="Search by bank name, account number, or holder name">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Account Type</label>
                <select class="form-control" [(ngModel)]="selectedAccountType">
                  <option value="">All Types</option>
                  <option *ngFor="let type of accountTypes" [value]="type.value">{{type.label}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>&nbsp;</label>
                <div>
                  <button type="button" class="btn btn-secondary mr-2" (click)="clearFilters()">Clear Filters</button>
                  <button type="button" class="btn btn-primary" (click)="openBankAccountModal()">
                    <i class="fa fa-plus"></i> Add Bank Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bank Accounts Table -->
      <div class="card">
        <div class="card-body">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover">
              <thead class="thead-dark">
                <tr>
                  <th>Bank Name</th>
                  <th>Account Number</th>
                  <th>Account Holder</th>
                  <th>Account Type</th>
                  <th>Current Balance</th>
                  <th>Branch</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let account of filteredBankAccounts" [class.table-secondary]="!account.active">
                  <td>{{account.bankName}}</td>
                  <td>{{account.accountNo}}</td>
                  <td>{{account.accountHolderName}}</td>
                  <td>{{getAccountTypeLabel(account.accountType)}}</td>
                  <td class="text-right">{{formatCurrency(account.currentBalance)}}</td>
                  <td>{{account.branch}}</td>
                  <td>
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-info" (click)="openTransactionModal(account)"
                              title="View Transactions">
                        <i class="fa fa-list"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-success" (click)="openBalanceCorrectionModal(account)"
                              title="Correct Balance">
                        <i class="fa fa-balance-scale"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-warning" (click)="openEditModal(account)"
                              title="Edit">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button type="button" class="btn btn-sm btn-danger" (click)="deleteBankAccount(account)"
                              title="Delete" *ngIf="account.active">
                        <i class="fa fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="filteredBankAccounts.length === 0">
                  <td colspan="7" class="text-center text-muted">
                    <div class="py-4">
                      <i class="fa fa-info-circle fa-2x mb-2"></i>
                      <p>No bank accounts found</p>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Bank Account Modal -->
<ng-template #bankAccountModal>
  <div class="modal-header">
    <h4 class="modal-title">{{isEditMode ? 'Edit' : 'Add'}} Bank Account</h4>
    <button type="button" class="close ms-auto" (click)="closeModal()">
      <span>&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form #bankAccountForm="ngForm">
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label>Bank Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control" [(ngModel)]="bankAccount.bankName"
                   name="bankName" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>Account Number <span class="text-danger">*</span></label>
            <input type="text" class="form-control" [(ngModel)]="bankAccount.accountNo"
                   name="accountNo" required>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label>Account Holder Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control" [(ngModel)]="bankAccount.accountHolderName"
                   name="accountHolderName" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>Account Type <span class="text-danger">*</span></label>
            <select class="form-control" [(ngModel)]="bankAccount.accountType" name="accountType" required>
              <option value="">Select Account Type</option>
              <option *ngFor="let type of accountTypes" [value]="type.value">{{type.label}}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label>Branch</label>
            <input type="text" class="form-control" [(ngModel)]="bankAccount.branch" name="branch">
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>Opening Balance</label>
            <input type="number" class="form-control" [(ngModel)]="bankAccount.openingBalance"
                   name="openingBalance" min="0" step="0.01">
          </div>
        </div>
      </div>
      <div class="form-group">
        <label>Remarks</label>
        <textarea class="form-control" [(ngModel)]="bankAccount.remark" name="remark" rows="3"></textarea>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveBankAccount()" [disabled]="loading">
      <span *ngIf="loading" class="spinner-border spinner-border-sm mr-2"></span>
      {{isEditMode ? 'Update' : 'Save'}}
    </button>
  </div>
</ng-template>

<!-- Transaction Modal -->
<ng-template #transactionModal>
  <div class="modal-header">
    <h4 class="modal-title">Bank Account Transactions - {{selectedBankAccount?.bankName}} ({{selectedBankAccount?.accountNo}})</h4>
    <button type="button" class="close ms-auto" (click)="closeModal()">
      <span>&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <!-- Current Balance Display -->
    <div class="alert alert-info">
      <strong>Current Balance: {{formatCurrency(selectedBankAccount?.currentBalance)}}</strong>
    </div>

    <!-- Add Transaction Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">Add New Transaction</h6>
      </div>
      <div class="card-body">
        <form #transactionForm="ngForm">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>Type</label>
                <select class="form-control" [(ngModel)]="selectedTransactionType" name="transactionType">
                  <option value="CREDIT">Credit (+)</option>
                  <option value="DEBIT">Debit (-)</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Amount</label>
                <input type="number" class="form-control" [(ngModel)]="transactionAmount"
                       name="amount" min="0.01" step="0.01">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Reference No</label>
                <input type="text" class="form-control" [(ngModel)]="referenceNo" name="referenceNo">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>Third Party</label>
                <input type="text" class="form-control" [(ngModel)]="thirdParty" name="thirdParty">
              </div>
            </div>
          </div>
          <div class="form-group">
            <label>Description</label>
            <input type="text" class="form-control" [(ngModel)]="transactionDescription" name="description">
          </div>
          <button type="button" class="btn btn-success" (click)="createTransaction()">
            <i class="fa fa-plus"></i> Add Transaction
          </button>
        </form>
      </div>
    </div>

    <!-- Transactions Table -->
    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
      <table class="table table-sm table-striped">
        <thead class="thead-light sticky-top">
          <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Amount</th>
            <th>Description</th>
            <th>Reference</th>
            <th>Third Party</th>
            <th>Balance</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let transaction of transactionList">
            <td>{{transaction.transactionDate | date:'short'}}</td>
            <td>
              <span [class]="getTransactionTypeClass(transaction.transactionType)">
                {{transaction.transactionType}}
              </span>
            </td>
            <td class="text-right" [class]="getTransactionTypeClass(transaction.transactionType)">
              {{formatCurrency(transaction.amount)}}
            </td>
            <td>{{transaction.description}}</td>
            <td>{{transaction.referenceNo}}</td>
            <td>{{transaction.thirdParty}}</td>
            <td class="text-right">{{formatCurrency(transaction.balanceAfterTransaction)}}</td>
          </tr>
          <tr *ngIf="transactionList.length === 0">
            <td colspan="7" class="text-center text-muted">No transactions found</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">Close</button>
  </div>
</ng-template>

<!-- Balance Correction Modal -->
<ng-template #balanceCorrectionModal>
  <div class="modal-header">
    <h4 class="modal-title">Correct Bank Balance - {{selectedBankAccountForCorrection?.bankName}} ({{selectedBankAccountForCorrection?.accountNo}})</h4>
    <button type="button" class="close ms-auto" (click)="closeModal()">
      <span>&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="alert alert-warning">
      <strong>Warning:</strong> Balance correction will create an audit trail and record an action for reporting purposes.
    </div>

    <div class="alert alert-info">
      <strong>Current Balance:</strong> {{formatCurrency(selectedBankAccountForCorrection?.currentBalance)}}
    </div>

    <form #balanceCorrectionForm="ngForm">
      <div class="form-group">
        <label>New Balance <span class="text-danger">*</span></label>
        <input type="number" class="form-control" [(ngModel)]="correctionAmount"
               name="correctionAmount" required min="0" step="0.01">
      </div>

      <div class="form-group">
        <label>Reason for Correction <span class="text-danger">*</span></label>
        <textarea class="form-control" [(ngModel)]="correctionReason"
                  name="correctionReason" required rows="3"
                  placeholder="Please provide a detailed reason for this balance correction..."></textarea>
      </div>

      <div class="form-group">
        <label>Balance Difference</label>
        <div class="form-control-plaintext">
          <span [class]="(correctionAmount - selectedBankAccountForCorrection?.currentBalance) >= 0 ? 'text-success' : 'text-danger'">
            {{formatCurrency(correctionAmount - selectedBankAccountForCorrection?.currentBalance)}}
          </span>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="correctBalance()" [disabled]="loading">
      <span *ngIf="loading" class="spinner-border spinner-border-sm mr-2"></span>
      Correct Balance
    </button>
  </div>
</ng-template>
