<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Liability Management</h2>
    </div>
  </div>

  <div class="row">
    <!-- Create Liability Form -->
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Add/Edit Liability</h5>
        </div>
        <div class="card-body">
          <form #liabilityForm="ngForm">
            <!-- Liability Name -->
            <div class="mb-3">
              <label class="form-label">Liability Name *</label>
              <input type="text" 
                     class="form-control" 
                     name="liabilityName"
                     [(ngModel)]="liability.liabilityName"
                     required
                     placeholder="Enter liability name">
            </div>

            <!-- Liability Type -->
            <div class="mb-3">
              <label class="form-label">Liability Type *</label>
              <select class="form-select" 
                      name="liabilityType"
                      [(ngModel)]="liability.liabilityType"
                      required>
                <option value="CURRENT">Current Liability</option>
                <option value="LONG_TERM">Long-term Liability</option>
              </select>
            </div>

            <!-- Category -->
            <div class="mb-3">
              <label class="form-label">Category</label>
              <select class="form-select" 
                      name="category"
                      [(ngModel)]="liability.category">
                <option value="">Select Category</option>
                <option *ngFor="let cat of categories" [value]="cat">{{ cat }}</option>
              </select>
            </div>

            <!-- Creditor Name -->
            <div class="mb-3">
              <label class="form-label">Creditor Name *</label>
              <input type="text" 
                     class="form-control" 
                     name="creditorName"
                     [(ngModel)]="liability.creditorName"
                     required
                     placeholder="Enter creditor name">
            </div>

            <!-- Original Amount -->
            <div class="mb-3">
              <label class="form-label">Original Amount *</label>
              <input type="number" 
                     class="form-control" 
                     name="originalAmount"
                     [(ngModel)]="liability.originalAmount"
                     required
                     min="0"
                     step="0.01"
                     placeholder="Enter original amount">
            </div>

            <!-- Interest Rate -->
            <div class="mb-3">
              <label class="form-label">Interest Rate (%)</label>
              <input type="number" 
                     class="form-control" 
                     name="interestRate"
                     [(ngModel)]="liability.interestRate"
                     min="0"
                     max="100"
                     step="0.1"
                     placeholder="Enter interest rate">
            </div>

            <!-- Start Date -->
            <div class="mb-3">
              <label class="form-label">Start Date</label>
              <input type="date" 
                     class="form-control" 
                     name="startDate"
                     [(ngModel)]="liability.startDate">
            </div>

            <!-- Due Date -->
            <div class="mb-3">
              <label class="form-label">Due Date</label>
              <input type="date" 
                     class="form-control" 
                     name="dueDate"
                     [(ngModel)]="liability.dueDate">
            </div>

            <!-- Payment Frequency -->
            <div class="mb-3">
              <label class="form-label">Payment Frequency</label>
              <select class="form-select" 
                      name="paymentFrequency"
                      [(ngModel)]="liability.paymentFrequency">
                <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                  {{ freq.label }}
                </option>
              </select>
            </div>

            <!-- Priority -->
            <div class="mb-3">
              <label class="form-label">Priority</label>
              <select class="form-select" 
                      name="priority"
                      [(ngModel)]="liability.priority">
                <option *ngFor="let priority of priorities" [value]="priority.value">
                  {{ priority.label }}
                </option>
              </select>
            </div>

            <!-- Description -->
            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea class="form-control" 
                        name="description"
                        [(ngModel)]="liability.description"
                        rows="3"
                        placeholder="Enter liability description"></textarea>
            </div>

            <!-- Buttons -->
            <div class="d-flex gap-2 justify-content-end">
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit" 
                      class="btn btn-primary" 
                      [disabled]="!liabilityForm.valid"
                      (click)="save(liabilityForm)">
                <i class="fas fa-save me-1"></i>Save Liability
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Liabilities List -->
    <div class="col-lg-7">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Liabilities</h5>
            <div class="d-flex gap-2">
              <!-- Filters -->
              <select class="form-select form-select-sm" 
                      [(ngModel)]="typeFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option *ngFor="let type of liabilityTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
              <select class="form-select form-select-sm" 
                      [(ngModel)]="statusFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
              <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                <i class="fas fa-times"></i> Clear
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Liability</th>
                  <th>Type</th>
                  <th>Creditor</th>
                  <th>Original Amount</th>
                  <th>Current Balance</th>
                  <th>Status</th>
                  <th>Due Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let liability of liabilities; let i = index"
                    [class.table-active]="selectedLiability?.id === liability.id"
                    (click)="selectLiability(liability, i)"
                    style="cursor: pointer;">
                  <td>
                    <div>
                      <strong>{{ liability.liabilityName }}</strong>
                      <br>
                      <small class="text-muted">{{ liability.category || 'No Category' }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [class]="getTypeClass(liability.liabilityType)">
                      {{ liability.liabilityType }}
                    </span>
                  </td>
                  <td>{{ liability.creditorName }}</td>
                  <td>{{ formatCurrency(liability.originalAmount) }}</td>
                  <td>
                    <div>
                      {{ formatCurrency(liability.currentBalance) }}
                      <br>
                      <small class="text-muted" *ngIf="liability.interestRate > 0">
                        {{ liability.interestRate }}% interest
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [class]="getStatusClass(liability.status)">
                      {{ liability.status }}
                    </span>
                    <div *ngIf="liability.status === 'ACTIVE' && calculateDaysOverdue(liability.dueDate) > 0">
                      <small class="text-danger">{{ calculateDaysOverdue(liability.dueDate) }} days overdue</small>
                    </div>
                  </td>
                  <td>
                    <div>
                      {{ formatDate(liability.dueDate) }}
                      <br>
                      <small [class]="getPriorityClass(liability.priority)">
                        <i class="fas fa-flag"></i> {{ liability.priority }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary btn-sm" 
                              (click)="editLiability(); $event.stopPropagation()"
                              title="Edit">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-outline-success btn-sm" 
                              *ngIf="liability.status === 'ACTIVE'"
                              (click)="$event.stopPropagation()"
                              title="Make Payment"
                              data-bs-toggle="modal" 
                              data-bs-target="#paymentModal">
                        <i class="fas fa-money-bill"></i>
                      </button>
                      <button class="btn btn-outline-info btn-sm" 
                              *ngIf="liability.interestRate > 0"
                              (click)="calculateInterest(); $event.stopPropagation()"
                              title="Calculate Interest">
                        <i class="fas fa-calculator"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="liabilities.length === 0">
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No liabilities found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer" *ngIf="collectionSize > pageSize">
          <ngb-pagination 
            [(page)]="page" 
            [pageSize]="pageSize" 
            [collectionSize]="collectionSize"
            (pageChange)="pageChanged($event)"
            [maxSize]="5"
            [rotate]="true"
            class="d-flex justify-content-center">
          </ngb-pagination>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Liability Details -->
  <div class="row mt-4" *ngIf="selectedLiability">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Liability Details</h5>
            <button class="btn btn-success btn-sm" 
                    *ngIf="selectedLiability.status === 'ACTIVE'"
                    data-bs-toggle="modal" 
                    data-bs-target="#paymentModal">
              <i class="fas fa-money-bill me-1"></i>Make Payment
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Liability Name:</strong></td>
                  <td>{{ selectedLiability.liabilityName }}</td>
                </tr>
                <tr>
                  <td><strong>Type:</strong></td>
                  <td>
                    <span class="badge" [class]="getTypeClass(selectedLiability.liabilityType)">
                      {{ selectedLiability.liabilityType }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Category:</strong></td>
                  <td>{{ selectedLiability.category || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Creditor:</strong></td>
                  <td>{{ selectedLiability.creditorName }}</td>
                </tr>
                <tr>
                  <td><strong>Original Amount:</strong></td>
                  <td>{{ formatCurrency(selectedLiability.originalAmount) }}</td>
                </tr>
                <tr>
                  <td><strong>Current Balance:</strong></td>
                  <td>{{ formatCurrency(selectedLiability.currentBalance) }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Status:</strong></td>
                  <td>
                    <span class="badge" [class]="getStatusClass(selectedLiability.status)">
                      {{ selectedLiability.status }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Interest Rate:</strong></td>
                  <td>{{ selectedLiability.interestRate || 0 }}%</td>
                </tr>
                <tr>
                  <td><strong>Start Date:</strong></td>
                  <td>{{ formatDate(selectedLiability.startDate) }}</td>
                </tr>
                <tr>
                  <td><strong>Due Date:</strong></td>
                  <td>{{ formatDate(selectedLiability.dueDate) }}</td>
                </tr>
                <tr>
                  <td><strong>Payment Frequency:</strong></td>
                  <td>{{ selectedLiability.paymentFrequency || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Priority:</strong></td>
                  <td>
                    <span [class]="getPriorityClass(selectedLiability.priority)">
                      <i class="fas fa-flag"></i> {{ selectedLiability.priority }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
          </div>
          <div class="row" *ngIf="selectedLiability.description">
            <div class="col-12">
              <strong>Description:</strong>
              <p class="mt-2">{{ selectedLiability.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Make Payment</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" *ngIf="selectedLiability">
        <div class="mb-3">
          <label class="form-label">Liability: <strong>{{ selectedLiability.liabilityName }}</strong></label>
          <p class="text-muted">Current Balance: {{ formatCurrency(selectedLiability.currentBalance) }}</p>
        </div>
        
        <div class="mb-3">
          <label class="form-label">Payment Amount *</label>
          <input type="number" 
                 class="form-control" 
                 [(ngModel)]="paymentAmount"
                 [max]="selectedLiability.currentBalance"
                 min="0"
                 step="0.01"
                 placeholder="Enter payment amount">
        </div>
        
        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control" 
                    [(ngModel)]="paymentNotes"
                    rows="3"
                    placeholder="Enter payment notes (optional)"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" 
                class="btn btn-success" 
                [disabled]="!paymentAmount || paymentAmount <= 0"
                (click)="makePayment()"
                data-bs-dismiss="modal">
          <i class="fas fa-money-bill me-1"></i>Record Payment
        </button>
      </div>
    </div>
  </div>
</div>
