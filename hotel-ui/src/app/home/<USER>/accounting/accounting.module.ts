import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ModalModule } from 'ngx-bootstrap/modal';
import {AccountingRoutingModule, accountRouteParams} from './accounting-routing.module';


@NgModule({
  declarations: [
    accountRouteParams
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    ModalModule.forRoot(),
    AccountingRoutingModule
  ]
})
export class AccountingModule { }
