import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

@Component({
standalone: false,
  selector: 'app-help-modal',
  templateUrl: './help-modal.component.html',
  styleUrls: ['./help-modal.component.css']
})
export class HelpModalComponent implements OnInit {

  activeTab: string = 'getting-started';
  searchQuery: string = '';
  filteredFaqs: any[] = [];

  faqs = [
    {
      id: 1,
      question: 'How do I add a new item to inventory?',
      questionSn: 'ඉන්වෙන්ටරියට නව භාණ්ඩයක් එකතු කරන්නේ කෙසේද?',
      answer: 'Go to Inventory > Create Item, fill in the required details like name, category, price, and click Save.',
      answerSn: 'ඉන්වෙන්ටරි > භාණ්ඩ නිර්මාණය වෙත ගොස්, නම, කාණ්ඩය, මිල වැනි අවශ්‍ය විස්තර පුරවා සුරකින්න ක්ලික් කරන්න.',
      category: 'inventory'
    },
    {
      id: 2,
      question: 'How do I create a sales invoice?',
      questionSn: 'විකුණුම් ඉන්වොයිසයක් නිර්මාණය කරන්නේ කෙසේද?',
      answer: 'Navigate to Trade > Create Sales Invoice, select customer, add items, and process the payment.',
      answerSn: 'වෙළඳාම > විකුණුම් ඉන්වොයිස නිර්මාණය වෙත ගොස්, ගනුදෙනුකරු තෝරා, භාණ්ඩ එකතු කර, ගෙවීම සකසන්න.',
      category: 'sales'
    },
    {
      id: 3,
      question: 'How do I generate reports?',
      questionSn: 'වාර්තා ජනනය කරන්නේ කෙසේද?',
      answer: 'Go to Reports section, select the type of report you need, set date range and filters, then click Generate.',
      answerSn: 'වාර්තා කොටසට ගොස්, ඔබට අවශ්‍ය වාර්තා වර්ගය තෝරා, දින පරාසය සහ පෙරහන් සකසා, ජනනය කරන්න ක්ලික් කරන්න.',
      category: 'reports'
    },
    {
      id: 4,
      question: 'How do I manage user permissions?',
      questionSn: 'පරිශීලක අවසර කළමනාකරණය කරන්නේ කෙසේද?',
      answer: 'Access Admin > User Management, select a user, and configure their role and permissions.',
      answerSn: 'පරිපාලන > පරිශීලක කළමනාකරණය වෙත ගොස්, පරිශීලකයෙකු තෝරා, ඔවුන්ගේ භූමිකාව සහ අවසර සකසන්න.',
      category: 'admin'
    },
    {
      id: 5,
      question: 'How do I backup my data?',
      questionSn: 'මගේ දත්ත උපස්ථ කරන්නේ කෙසේද?',
      answer: 'Go to Admin > System Settings > Backup, select backup options and click Create Backup.',
      answerSn: 'පරිපාලන > පද්ධති සැකසුම් > උපස්ථ වෙත ගොස්, උපස්ථ විකල්ප තෝරා උපස්ථ නිර්මාණය කරන්න ක්ලික් කරන්න.',
      category: 'admin'
    },
    {
      id: 6,
      question: 'How do I change the language?',
      questionSn: 'භාෂාව වෙනස් කරන්නේ කෙසේද?',
      answer: 'Click on the language switcher in the top right corner and select your preferred language.',
      answerSn: 'ඉහළ දකුණු කෙළවරේ ඇති භාෂා මාරුව ක්ලික් කර ඔබගේ කැමති භාෂාව තෝරන්න.',
      category: 'general'
    }
  ];

  videoTutorials = [
    {
      id: 1,
      title: 'Getting Started with the System',
      titleSn: 'පද්ධතිය සමඟ ආරම්භ කිරීම',
      description: 'Learn the basics of navigating and using the system',
      descriptionSn: 'පද්ධතිය සැරිසැරීම සහ භාවිතය පිළිබඳ මූලික කරුණු ඉගෙන ගන්න',
      duration: '5:30',
      thumbnail: 'assets/images/tutorial-1.jpg',
      videoUrl: 'https://youtube.com/watch?v=example1'
    },
    {
      id: 2,
      title: 'Inventory Management',
      titleSn: 'ඉන්වෙන්ටරි කළමනාකරණය',
      description: 'Complete guide to managing your inventory',
      descriptionSn: 'ඔබේ ඉන්වෙන්ටරිය කළමනාකරණය කිරීම සඳහා සම්පූර්ණ මාර්ගෝපදේශය',
      duration: '8:45',
      thumbnail: 'assets/images/tutorial-2.jpg',
      videoUrl: 'https://youtube.com/watch?v=example2'
    },
    {
      id: 3,
      title: 'Sales and Invoicing',
      titleSn: 'විකුණුම් සහ ඉන්වොයිස්',
      description: 'How to process sales and create invoices',
      descriptionSn: 'විකුණුම් සකසන ආකාරය සහ ඉන්වොයිස් නිර්මාණය',
      duration: '7:20',
      thumbnail: 'assets/images/tutorial-3.jpg',
      videoUrl: 'https://youtube.com/watch?v=example3'
    },
    {
      id: 4,
      title: 'Reports and Analytics',
      titleSn: 'වාර්තා සහ විශ්ලේෂණ',
      description: 'Generate and analyze business reports',
      descriptionSn: 'ව්‍යාපාරික වාර්තා ජනනය සහ විශ්ලේෂණය',
      duration: '6:15',
      thumbnail: 'assets/images/tutorial-4.jpg',
      videoUrl: 'https://youtube.com/watch?v=example4'
    }
  ];

  constructor(
    public modalRef: BsModalRef,
    private translateService: TranslateService
  ) { }

  ngOnInit(): void {
    this.filteredFaqs = this.faqs;
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  searchFaqs(): void {
    if (!this.searchQuery.trim()) {
      this.filteredFaqs = this.faqs;
      return;
    }

    const query = this.searchQuery.toLowerCase();
    this.filteredFaqs = this.faqs.filter(faq =>
      faq.question.toLowerCase().includes(query) ||
      faq.questionSn.includes(query) ||
      faq.answer.toLowerCase().includes(query) ||
      faq.answerSn.includes(query)
    );
  }

  getCurrentLang(): string {
    return this.translateService.currentLang || 'en';
  }

  openVideoTutorial(videoUrl: string): void {
    window.open(videoUrl, '_blank');
  }

  closeModal(): void {
    this.modalRef.hide();
  }
}
