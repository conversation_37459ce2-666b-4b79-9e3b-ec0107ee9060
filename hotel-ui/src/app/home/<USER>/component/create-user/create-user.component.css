

/* Custom styles for create-user component */

/* Modal specific styles */
.p-3 {
  padding: 1rem !important;
}

/* Close button styling */
.close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.close:hover {
  opacity: 1;
}

/* Section styling */
.section-title {
  font-weight: 600;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 16px;
  font-size: 1.1rem;
}

/* Form styling */
.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Button styling */
.btn {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
}



/* Custom switch styling */
.custom-control-input:checked ~ .custom-control-label::before {
  background-color: #28a745;
  border-color: #28a745;
}

/* Tag input styling */
tag-input {
  margin-bottom: 1rem;
}

tag-input.ng-invalid.ng-touched .ng2-tag-input {
  border-bottom: 1px solid #dc3545 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .section-title {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .form-group {
    margin-bottom: 0.75rem;
  }

  .form-control {
    font-size: 0.9rem;
    padding: 0.375rem 0.5rem;
  }

  .mb-4 {
    margin-bottom: 1rem !important;
  }

  .form-text {
    font-size: 0.7rem;
  }

  .custom-control-label {
    font-size: 0.9rem;
  }
}
