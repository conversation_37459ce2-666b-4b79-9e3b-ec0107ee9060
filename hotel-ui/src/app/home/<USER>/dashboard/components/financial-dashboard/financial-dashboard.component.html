<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Financial Dashboard</h2>
    </div>
  </div>

  <!-- Financial Position Summary -->
  <div class="row mb-4">
    <div class="col-12">
      <h4 class="text-secondary mb-3">Financial Position</h4>
    </div>

    <!-- Cash & Bank -->
    <div class="col-md-3 mb-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Cash & Bank</h6>
              <h4 class="mb-0">{{ formatCurrency(totalCashBalance + totalBankBalance) }}</h4>
            </div>
            <div class="align-self-center">
              <i class="fa fa-money fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Accounts Receivable -->
    <div class="col-md-3 mb-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Accounts Receivable</h6>
              <h4 class="mb-0">{{ formatCurrency(totalAccountsReceivable) }}</h4>
            </div>
            <div class="align-self-center">
              <i class="fa fa-arrow-down fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Accounts Payable -->
    <div class="col-md-3 mb-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Accounts Payable</h6>
              <h4 class="mb-0">{{ formatCurrency(totalAccountsPayable) }}</h4>
            </div>
            <div class="align-self-center">
              <i class="fa fa-arrow-up fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Petty Cash -->
    <div class="col-md-3 mb-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Petty Cash</h6>
              <h4 class="mb-0">{{ formatCurrency(totalPettyCash) }}</h4>
            </div>
            <div class="align-self-center">
              <i class="fa fa-wallet fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Profit & Loss Summary -->
  <div class="row mb-4">
    <div class="col-12">
      <h4 class="text-secondary mb-3">Profit & Loss Summary</h4>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Sales Revenue</h6>
          <h4 class="text-primary">{{ formatCurrency(totalSalesRevenue) }}</h4>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Total Expenses</h6>
          <h4 class="text-danger">{{ formatCurrency(totalExpenses) }}</h4>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Gross Profit</h6>
          <h4 [class]="getStatusClass(grossProfit)">{{ formatCurrency(grossProfit) }}</h4>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Net Profit</h6>
          <h4 [class]="getStatusClass(netProfit)">{{ formatCurrency(netProfit) }}</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Financial Ratios -->
  <div class="row mb-4">
    <div class="col-12">
      <h4 class="text-secondary mb-3">Key Financial Ratios</h4>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Current Ratio</h6>
          <h4 class="text-info">{{ formatRatio(currentRatio) }}</h4>
          <small class="text-muted">Current Assets / Current Liabilities</small>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Quick Ratio</h6>
          <h4 class="text-info">{{ formatRatio(quickRatio) }}</h4>
          <small class="text-muted">(Current Assets - Inventory) / Current Liabilities</small>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Gross Profit Margin</h6>
          <h4 class="text-success">{{ formatPercentage(grossProfitMargin) }}</h4>
          <small class="text-muted">Gross Profit / Sales Revenue</small>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-3">
      <div class="card">
        <div class="card-body text-center">
          <h6 class="card-title text-muted">Net Profit Margin</h6>
          <h4 class="text-success">{{ formatPercentage(netProfitMargin) }}</h4>
          <small class="text-muted">Net Profit / Sales Revenue</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Breakdown -->
  <div class="row">
    <!-- Balance Sheet Summary -->
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Balance Sheet Summary</h5>
        </div>
        <div class="card-body">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>Assets</th>
                <th class="text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Cash</td>
                <td class="text-right">{{ formatCurrency(totalCashBalance) }}</td>
              </tr>
              <tr>
                <td>Bank</td>
                <td class="text-right">{{ formatCurrency(totalBankBalance) }}</td>
              </tr>
              <tr>
                <td>Accounts Receivable</td>
                <td class="text-right">{{ formatCurrency(totalAccountsReceivable) }}</td>
              </tr>
              <tr>
                <td>Petty Cash</td>
                <td class="text-right">{{ formatCurrency(totalPettyCash) }}</td>
              </tr>
              <tr>
                <td>Inventory</td>
                <td class="text-right">{{ formatCurrency(totalInventoryValue) }}</td>
              </tr>
              <tr class="table-active font-weight-bold">
                <td><strong>Total Assets</strong></td>
                <td class="text-right"><strong>{{ formatCurrency(getTotalAssets()) }}</strong></td>
              </tr>
            </tbody>
          </table>

          <table class="table table-sm mt-3">
            <thead>
              <tr>
                <th>Liabilities</th>
                <th class="text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Accounts Payable</td>
                <td class="text-right">{{ formatCurrency(totalAccountsPayable) }}</td>
              </tr>
              <tr class="table-active font-weight-bold">
                <td><strong>Total Liabilities</strong></td>
                <td class="text-right"><strong>{{ formatCurrency(getTotalLiabilities()) }}</strong></td>
              </tr>
              <tr class="table-success font-weight-bold">
                <td><strong>Net Worth</strong></td>
                <td class="text-right"><strong>{{ formatCurrency(getNetWorth()) }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Petty Cash Funds -->
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Petty Cash Funds</h5>
          <a routerLink="/home/<USER>/petty-cash" class="btn btn-sm btn-outline-primary">
            <i class="fa fa-cog"></i> Manage
          </a>
        </div>
        <div class="card-body">
          <div *ngIf="loadingPettyCash" class="text-center">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <div *ngIf="!loadingPettyCash">
            <table class="table table-sm" *ngIf="pettyCashFunds.length > 0">
              <thead>
                <tr>
                  <th>Location</th>
                  <th>Custodian</th>
                  <th class="text-right">Balance</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let fund of pettyCashFunds">
                  <td>{{ fund.location }}</td>
                  <td>{{ fund.custodian }}</td>
                  <td class="text-right">{{ formatCurrency(fund.currentBalance) }}</td>
                </tr>
                <tr class="table-active font-weight-bold">
                  <td colspan="2"><strong>Total</strong></td>
                  <td class="text-right"><strong>{{ formatCurrency(totalPettyCash) }}</strong></td>
                </tr>
              </tbody>
            </table>

            <div *ngIf="pettyCashFunds.length === 0" class="text-center text-muted">
              <p>No petty cash funds found</p>
              <a routerLink="/home/<USER>/petty-cash" class="btn btn-primary btn-sm">
                <i class="fa fa-plus"></i> Create First Fund
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Accounting Management -->
  <div class="row mb-4">
    <div class="col-12">
      <h4 class="text-secondary mb-3">Accounting Management</h4>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Core Accounting</h5>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            <a routerLink="/home/<USER>/accounts-receivable" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Accounts Receivable</h6>
                <small><i class="fas fa-arrow-down"></i></small>
              </div>
              <p class="mb-1">Manage customer outstanding balances</p>
            </a>
            <a routerLink="/home/<USER>/accounts-payable" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Accounts Payable</h6>
                <small><i class="fas fa-arrow-up"></i></small>
              </div>
              <p class="mb-1">Manage supplier outstanding balances</p>
            </a>
            <a routerLink="/home/<USER>/petty-cash" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Petty Cash Management</h6>
                <small><i class="fas fa-coins"></i></small>
              </div>
              <p class="mb-1">Manage petty cash funds and transactions</p>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Asset, Liability & Equity</h5>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            <a routerLink="/home/<USER>/asset-management" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Asset Management</h6>
                <small><i class="fas fa-building"></i></small>
              </div>
              <p class="mb-1">Manage company assets (equipment, furniture, etc.)</p>
            </a>
            <a routerLink="/home/<USER>/liability-management" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Liability Management</h6>
                <small><i class="fas fa-credit-card"></i></small>
              </div>
              <p class="mb-1">Manage loans, debts, and obligations</p>
            </a>
            <a routerLink="/home/<USER>/equity-management" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">Equity Management</h6>
                <small><i class="fas fa-chart-pie"></i></small>
              </div>
              <p class="mb-1">Manage owner's capital and retained earnings</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Financial Reports Link -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-body text-center">
          <h5 class="card-title">Financial Reports</h5>
          <p class="card-text">Access comprehensive financial reports in the Report module</p>
          <a routerLink="/home/<USER>" class="btn btn-primary">
            <i class="fas fa-chart-bar"></i> Go to Reports Module
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
