<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> <PERSON>ton (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Racks List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Racks</h5>
            <div class="input-group" style="max-width: 300px;">
              <input [(ngModel)]="keyRack"
                     [typeahead]="racks"
                     (typeaheadLoading)="loadRacks()"
                     (typeaheadOnSelect)="setSelectedRack($event)"
                     [typeaheadOptionsLimit]="7"
                     typeaheadOptionField="rackNo"
                     placeholder="Search racks..."
                     autocomplete="off"
                     class="form-control"
                     name="rackSearch">
              <button class="btn btn-primary" type="button">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-professional table-hover table-striped table-hover mb-0">
              <thead class="table-light">
              <tr>
                <th class="border-0 fw-semibold">Rack No</th>
                <th class="border-0 fw-semibold">Description</th>
                <th class="border-0 fw-semibold text-center">Status</th>
                <th class="border-0 fw-semibold text-center">Actions</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let rack of racks; let i = index"
                  class="table-row"
                  [class.active]="i === selectedRow"
                  (click)="rackDetail(rack, i)">
                <td class="align-middle">
                  <span class="entity-name">{{ rack.rackNo }}</span>
                </td>
                <td class="align-middle">
                  <span class="text-muted">{{ rack.description || 'No description' }}</span>
                </td>
                <td class="align-middle text-center">
                    <span class="badge" [class]="rack.active ? 'bg-success' : 'bg-danger'">
                      <i class="fas" [class]="rack.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                      {{ rack.active ? 'Active' : 'Inactive' }}
                    </span>
                </td>
                <td class="align-middle text-center">
                  <button class="btn btn-sm btn-primary"
                          (click)="rackDetail(rack, i); $event.stopPropagation()">
                    <i class="fas fa-edit"></i>
                  </button>
                </td>
              </tr>
              <tr *ngIf="racks?.length === 0">
                <td colspan="100%" class="text-center empty-state-professional">
                  <i class="fas fa-warehouse fa-2x mb-3 d-block"></i>
                  No racks found
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="card-footer bg-white border-top" *ngIf="collectionSize > pageSize">
          <div class="d-flex justify-content-center">
            <pagination class="pagination pagination-sm justify-content-center pagination-professional"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="5"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Rack Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            {{ rack.id ? 'Edit Rack' : 'Rack Details' }}
          </h5>
        </div>

        <div class="card-body professional-card-body">
          <form #manageRackForm="ngForm" (ngSubmit)="saveRack()" class="form-professional">

            <!-- Rack Number Field -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold">Rack Number</label>
              <input type="text"
                     class="form-control"
                     [(ngModel)]="rack.rackNo"
                     name="rackNo"
                     placeholder="Enter rack number"
                     required>
            </div>

            <!-- Description Field -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold">Description</label>
              <textarea class="form-control"
                        rows="3"
                        [(ngModel)]="rack.description"
                        name="description"
                        placeholder="Enter rack description"></textarea>
            </div>

            <!-- Status Toggle -->
            <div class="mb-3 mb-4">
              <label class="form-label fw-semibold mb-3">Rack Settings</label>

              <div class="status-toggles">
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="activeSwitch"
                         [(ngModel)]="rack.active"
                         name="active">
                  <label class="form-check-label fw-semibold" for="activeSwitch">
                    Active Status
                  </label>
                  <div class="form-text">Enable this rack for use</div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2 justify-content-end flex-wrap">
              <button type="button" class="btn btn-secondary" (click)="clear()">
                <i class="fas fa-eraser me-1"></i>Clear
              </button>
              <button type="button" class="btn btn-warning" (click)="updateRack()">
                <i class="fas fa-edit me-1"></i>Update
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>{{ rack.id ? 'Update' : 'Save' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>


