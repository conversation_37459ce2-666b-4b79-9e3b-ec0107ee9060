<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">
      {{ 'VIEW_STOCK.TITLE' | translate }}
      <span *ngIf="itemName" class="text-primary ms-2">
        - {{ itemName }}
      </span>
    </h2>
    <button *ngIf="isModal" type="button" class="btn-close" aria-label="Close"  (click)="closeModal()"></button>
  </div>
  <!-- Professional Filter Section -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
      <h6 class="mb-0 text-primary"><i class="fas fa-filter me-2"></i>Search & Filter Options</h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Warehouse</label>
          <select class="form-select" required #selectedWh="ngModel"
                  [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                  [(ngModel)]="selectedWarehouse" (change)="filterByWarehouse()">
            <option [ngValue]="">Select Warehouse</option>
            <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
          </select>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Search By Items</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemSearch"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItems()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="itemName"
                   placeholder="Search By Items"
                   autocomplete="off"
                   class="form-control" name="searchItem">
            <button class="btn btn-primary" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Search By Barcode</label>
          <div class="input-group">
            <input [(ngModel)]="barcode"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItemByCode()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="barcode"
                   autocomplete="off"
                   placeholder="Search By Barcode"
                   class="form-control" name="category">
            <button class="btn btn-primary" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Category</label>
          <input [(ngModel)]="keyItemCategory"
                 [typeahead]="itemCategories"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadOptionField="categoryName"
                 placeholder="Category"
                 autocomplete="off"
                 #category="ngModel"
                 class="form-control" name="category">
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Sub Category</label>
          <input [(ngModel)]="keySubCategory"
                 [typeahead]="subCategories"
                 (typeaheadLoading)="loadSubCategories()"
                 (typeaheadOnSelect)="setSelectedSubCategory($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="subCategoryName"
                 placeholder="Sub Category"
                 autocomplete="off"
                 class="form-control" name="subCategory">
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Brand</label>
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="name"
                 placeholder="Brand"
                 autocomplete="off"
                 class="form-control" name="brand">
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <div class="d-flex gap-2 justify-content-end">
            <button type="button" class="btn btn-secondary" (click)="clearFilters()">
              <i class="fa fa-refresh me-1"></i>Reset Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
    <div class="table-responsive" style="min-height: 400px;">
      <table class="table table-professional table-hover table-striped table-hover mt-2">
        <thead class="table-light">
        <tr class="text-center">
          <th class="d-none d-md-table-cell">Warehouse</th>
          <th>Barcode</th>
          <th>Item Name</th>
          <th class="d-none d-lg-table-cell">Sub Category</th>
          <th class="d-none d-md-table-cell">Selling Price</th>
          <th class="d-none d-md-table-cell">Dead Stock Level</th>
          <th>Quantity</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let stock of stocks,let i = index"
            (click)="selectStockRecord(stock,i)"
            [class.active]="i === selectedRow" class="text-center">
          <td class="d-none d-md-table-cell">{{stock.warehouseName}}</td>
          <td>{{stock.barcode}}</td>
          <td>{{stock.itemName}}</td>
          <td class="d-none d-lg-table-cell">{{stock.subCategoryName || 'N/A'}}</td>
          <td class="d-none d-md-table-cell">{{stock.sellingPrice}}</td>
          <td class="d-none d-md-table-cell">{{stock.deadStockLevel}}</td>
          <td>{{stock.quantity}}</td>
        </tr>
        <tr *ngIf="!loading && (!stocks || stocks.length === 0)">
          <td colspan="100%" class="text-center empty-state-professional">
            <div class="alert alert-info mb-0">
              <i class="fas fa-info-circle me-2"></i>
              No stock records found for this item.
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Mobile view for selected stock details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedStock && selectedStock.id">
      <div class="card bg-light">
        <div class="card-body professional-card-body">
          <h5 class="card-title professional-card-title">Selected Stock Details</h5>
          <div class="row g-2">
            <div class="col-6">
              <p class="mb-1 fw-bold">Warehouse:</p>
              <p>{{selectedStock.warehouseName}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Selling Price:</p>
              <p>{{selectedStock.sellingPrice}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Dead Stock Level:</p>
              <p>{{selectedStock.deadStockLevel}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Quantity:</p>
              <p>{{selectedStock.quantity}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12">
        <pagination class="pagination pagination pagination-sm justify-content-center justify-content-center pagination-professional"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)"
                    [maxSize]="5"
                    [boundaryLinks]="true" >
        </pagination>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12 text-end">
        <button type="button" class="btn btn-primary ms-2" (click)="clearTable()" >Reset Table</button>
        <button type="button" class="btn btn-danger ms-2" (click)="transferStock()" >Transfer</button>
        <button type="button" class="btn btn-danger ms-2" (click)="adjustStock()" >Adjust Stock</button>
      </div>
    </div>
  <!-- Bootstrap loading spinner -->
  <div *ngIf="loading" class="position-fixed w-100 h-100 d-flex justify-content-center align-items-center"
       style="top: 0; left: 0; background-color: rgba(0,0,0,0.3); z-index: 1050;">
    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>


