import { EventItem } from './event-item';
import { Facility } from './facility';
import { Customer } from '../../trade/model/customer';

export class Event {
  public id: string;
  public eventCode: string;
  public eventName: string;
  public eventDate: Date;
  public numberOfParticipants: number;
  public venue: Facility;
  public customer: Customer;
  public eventItems: EventItem[];
  public subTotal: number;
  public totalAmount: number;
  public description: string;
  public active: boolean;
  public createdDate: Date;
  public createdBy: string;

  constructor() {
    this.id = '';
    this.eventCode = '';
    this.eventName = '';
    this.eventDate = new Date();
    this.numberOfParticipants = 0;
    this.venue = new Facility();
    this.customer = new Customer();
    this.eventItems = [];
    this.subTotal = 0;
    this.totalAmount = 0;
    this.description = '';
    this.active = true;
    this.createdDate = new Date();
    this.createdBy = '';
  }

  /**
   * Calculate subtotal from event items
   */
  calculateSubTotal(): number {
    return this.eventItems.reduce((total, item) => total + item.totalPrice, 0);
  }

  /**
   * Calculate total amount (currently same as subtotal, can be extended for taxes/discounts)
   */
  calculateTotalAmount(): number {
    return this.calculateSubTotal();
  }

  /**
   * Update totals when event items change
   */
  updateTotals(): void {
    this.subTotal = this.calculateSubTotal();
    this.totalAmount = this.calculateTotalAmount();
  }

  /**
   * Add event item
   */
  addEventItem(eventItem: EventItem): void {
    this.eventItems.push(eventItem);
    this.updateTotals();
  }

  /**
   * Remove event item
   */
  removeEventItem(index: number): void {
    if (index >= 0 && index < this.eventItems.length) {
      this.eventItems.splice(index, 1);
      this.updateTotals();
    }
  }

  /**
   * Get total number of event items
   */
  getTotalItemsCount(): number {
    return this.eventItems.length;
  }

  /**
   * Get total quantity of all items
   */
  getTotalQuantity(): number {
    return this.eventItems.reduce((total, item) => total + item.quantity, 0);
  }
}
