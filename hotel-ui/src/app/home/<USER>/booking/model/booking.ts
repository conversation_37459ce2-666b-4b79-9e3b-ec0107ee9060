import {Facility} from "./facility";
import {MetaData} from "../../../core/model/metaData";
import {Customer} from "../../trade/model/customer";
import {SalesInvoiceRecord} from "../../trade/model/sales-invoice-record";
import {BoardType} from "./board-type";

export class Booking {
  public id: string;
  public invoiceNo: string;
  public bookingCode: string;
  public facility: Facility;
  public customer: Customer;
  public salesInvoiceRecords: Array<SalesInvoiceRecord>;
  public from: Date;
  public to: Date;
  public boardType: BoardType;
  public active: boolean;
  public roomBook: boolean;
  public todayBook: boolean;
  public bookingStatus: MetaData;
  public invoiceStatus: MetaData;
  public subTotal: number;
  public totalAmount: number;
  public price: number;
  public dueDate: Date;
  public facilityCharge: number;
  public totalDiscount: number;
  public payment: number;
  public cashBalance: number;
  public serviceCharge: number;
  public counterNo: string;
  public bookingDate: string;
  public invoiceDate: string;
  public createdDate: string;
  public createdBy: string;
  public lastModifiedDate: string;
  public lastModifiedBy: string;

  // Additional properties for enhanced booking management
  public numberOfGuests: number;
  public specialRequests: string;
  public checkInTime: Date;
  public checkOutTime: Date;
  public bookingSource: string; // Online, Phone, Walk-in, etc.
  public confirmationNumber: string;
  public notes: string;

  constructor() {
    this.id = '';
    this.invoiceNo = '';
    this.bookingCode = '';
    this.facility = new Facility();
    this.customer = null;
    this.salesInvoiceRecords = [];
    this.from = new Date();
    this.to = new Date();
    this.boardType = null;
    this.active = true;
    this.roomBook = false;
    this.todayBook = false;
    this.bookingStatus = null;
    this.invoiceStatus = null;
    this.subTotal = 0;
    this.totalAmount = 0;
    this.price = 0;
    this.dueDate = new Date();
    this.facilityCharge = 0;
    this.totalDiscount = 0;
    this.payment = 0;
    this.cashBalance = 0;
    this.serviceCharge = 0;
    this.counterNo = '';
    this.bookingDate = '';
    this.invoiceDate = '';
    this.createdDate = '';
    this.createdBy = '';
    this.lastModifiedDate = '';
    this.lastModifiedBy = '';
    this.numberOfGuests = 0;
    this.specialRequests = '';
    this.checkInTime = new Date();
    this.checkOutTime = new Date();
    this.bookingSource = '';
    this.confirmationNumber = '';
    this.notes = '';
  }
}
