import { FacilityType } from './facility-type';

export class Facility {
  public id: string;
  public facilityNo: string;
  public facilityName: string;
  public facilityCharge: number;
  public facilityType: FacilityType;
  public active: boolean;

  constructor() {
    this.id = '';
    this.facilityNo = '';
    this.facilityName = '';
    this.facilityCharge = 0;
    this.facilityType = new FacilityType();
    this.active = true;
  }
}
