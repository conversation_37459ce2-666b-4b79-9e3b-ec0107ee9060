import { Component, OnInit } from '@angular/core';
import { Facility } from '../../model/facility';
import { FacilityType } from '../../model/facility-type';
import { FacilityService } from '../../service/facility.service';
import { FacilityTypeService } from '../../service/facility-type.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-manage-facilities',
  templateUrl: './manage-facilities.component.html',
  styleUrls: ['./manage-facilities.component.css']
})
export class ManageFacilitiesComponent implements OnInit {

  // Facility management
  facility: Facility = new Facility();
  facilities: Facility[] = [];
  selectedRow: number = -1;

  // Facility types
  facilityTypes: FacilityType[] = [];
  selectedFacilityType: FacilityType;

  // Search and pagination
  keyFacilitySearch: string = '';
  currentPage: number = 1;
  pageSize: number = 10;
  totalFacilities: number = 0;

  // Form state
  isEditing: boolean = false;
  isActive: boolean = true;

  constructor(
    private facilityService: FacilityService,
    private facilityTypeService: FacilityTypeService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadFacilities();
    this.loadFacilityTypes();
  }

  /**
   * Load all facilities with pagination
   */
  loadFacilities(): void {
    this.facilityService.findAll(this.currentPage - 1, this.pageSize).subscribe(
      (result: any) => {
        if (result && result.content) {
          this.facilities = result.content || [];
          this.totalFacilities = result.totalElements || 0;
        } else if (result && Array.isArray(result)) {
          this.facilities = result;
          this.totalFacilities = result.length;
        } else {
          this.facilities = [];
          this.totalFacilities = 0;
        }
      },
      (error) => {
        console.error('Error loading facilities:', error);
        this.notificationService.showError('Failed to load facilities');
        this.facilities = [];
        this.totalFacilities = 0;
      }
    );
  }

  /**
   * Load all facility types
   */
  loadFacilityTypes(): void {
    this.facilityTypeService.findAllActive().subscribe(
      (data: FacilityType[]) => {
        this.facilityTypes = data || [];
      },
      (error) => {
        console.error('Error loading facility types:', error);
        this.notificationService.showError('Failed to load facility types');
        this.facilityTypes = [];
      }
    );
  }

  /**
   * Search facilities by name
   */
  searchFacilities(): void {
    if (this.keyFacilitySearch && this.keyFacilitySearch.trim() !== '') {
      this.facilityService.findByName(this.keyFacilitySearch).subscribe(
        (data: Facility[]) => {
          this.facilities = data || [];
          this.totalFacilities = this.facilities.length;
        },
        (error) => {
          console.error('Error searching facilities:', error);
          this.notificationService.showError('Failed to search facilities');
        }
      );
    } else {
      this.loadFacilities();
    }
  }

  /**
   * Select facility for editing
   */
  selectFacility(facility: Facility, index: number): void {
    this.facility = { ...facility }; // Create a copy
    this.selectedFacilityType = facility.facilityType;
    this.isActive = facility.active;
    this.selectedRow = index;
    this.isEditing = true;
  }

  /**
   * Save facility
   */
  saveFacility(): void {
    try {
      // Validate required fields
      if (!this.facility.facilityName || this.facility.facilityName.trim() === '') {
        this.notificationService.showError('Please enter facility name');
        return;
      }

      if (!this.facility.facilityNo || this.facility.facilityNo.trim() === '') {
        this.notificationService.showError('Please enter facility number');
        return;
      }

      if (!this.selectedFacilityType) {
        this.notificationService.showError('Please select facility type');
        return;
      }

      if (this.facility.facilityCharge < 0) {
        this.notificationService.showError('Facility charge cannot be negative');
        return;
      }

      // Set facility properties
      this.facility.facilityType = this.selectedFacilityType;
      this.facility.active = this.isActive;

      this.facilityService.save(this.facility).subscribe(
        (result: any) => {
          if (result.code === 200) {
            this.notificationService.showSuccess(result.message || 'Facility saved successfully');
            this.loadFacilities();
            this.clearForm();
          } else {
            this.notificationService.showError(result.message || 'Failed to save facility');
          }
        },
        (error) => {
          console.error('Error saving facility:', error);
          this.notificationService.showError('Error saving facility: ' + (error.message || 'Unknown error'));
        }
      );
    } catch (error) {
      console.error('Error in saveFacility:', error);
      this.notificationService.showError('Error saving facility');
    }
  }

  /**
   * Delete facility
   */
  deleteFacility(): void {
    if (!this.facility || !this.facility.id) {
      this.notificationService.showError('Please select a facility to delete');
      return;
    }

    if (confirm('Are you sure you want to delete this facility?')) {
      this.facilityService.delete(this.facility.id).subscribe(
        (result: any) => {
          if (result.code === 200) {
            this.notificationService.showSuccess('Facility deleted successfully');
            this.loadFacilities();
            this.clearForm();
          } else {
            this.notificationService.showError(result.message || 'Failed to delete facility');
          }
        },
        (error) => {
          console.error('Error deleting facility:', error);
          this.notificationService.showError('Error deleting facility');
        }
      );
    }
  }

  /**
   * Clear form
   */
  clearForm(): void {
    this.facility = new Facility();
    this.selectedFacilityType = null;
    this.isActive = true;
    this.selectedRow = -1;
    this.isEditing = false;
    this.keyFacilitySearch = '';
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadFacilities();
  }

  /**
   * Get facility type name
   */
  getFacilityTypeName(facilityType: FacilityType): string {
    return facilityType ? facilityType.name : 'No Type';
  }
}
