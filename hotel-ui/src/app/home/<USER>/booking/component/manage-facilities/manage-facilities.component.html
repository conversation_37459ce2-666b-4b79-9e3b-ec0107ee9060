<div class="container-fluid mt-3">
  <h2 class="component-title mb-4">MANAGE FACILITIES</h2>

  <div class="row g-4">
    <!-- Facilities List Section -->
    <div class="col-lg-8">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Facilities</h5>
            <div class="input-group" style="max-width: 300px;">
              <input [(ngModel)]="keyFacilitySearch"
                     (keyup.enter)="searchFacilities()"
                     placeholder="Search facilities..."
                     autocomplete="off"
                     class="form-control"
                     name="facilitySearch">
              <button class="btn btn-primary" type="button" (click)="searchFacilities()">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-professional table-hover table-striped table-hover mb-0">
              <thead class="table-light">
              <tr>
                <th class="border-0 fw-semibold">Facility No</th>
                <th class="border-0 fw-semibold">Facility Name</th>
                <th class="border-0 fw-semibold">Type</th>
                <th class="border-0 fw-semibold text-end">Charge</th>
                <th class="border-0 fw-semibold text-center">Status</th>
                <th class="border-0 fw-semibold text-center">Actions</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let facility of facilities; let i = index"
                  class="table-row"
                  [class.active]="i === selectedRow"
                  (click)="selectFacility(facility, i)">
                <td class="align-middle">
                  <span class="entity-name">{{ facility.facilityNo }}</span>
                </td>
                <td class="align-middle">
                  <span class="fw-bold">{{ facility.facilityName }}</span>
                </td>
                <td class="align-middle">
                  <span class="text-muted">{{ getFacilityTypeName(facility.facilityType) }}</span>
                </td>
                <td class="align-middle text-end">
                  <span class="fw-bold">{{ facility.facilityCharge | currency:'LKR':'symbol':'1.2-2' }}</span>
                </td>
                <td class="align-middle text-center">
                  <span class="badge" [class]="facility.active ? 'bg-success' : 'bg-danger'">
                    <i class="fas" [class]="facility.active ? 'fa-check-circle' : 'fa-times-circle'"></i>
                    {{ facility.active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="align-middle text-center">
                  <button class="btn btn-outline-primary btn-sm me-1"
                          (click)="selectFacility(facility, i); $event.stopPropagation()">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-outline-danger btn-sm"
                          (click)="selectFacility(facility, i); deleteFacility(); $event.stopPropagation()">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
              <tr *ngIf="facilities.length === 0">
                <td colspan="6" class="text-center text-muted py-4">
                  <i class="fas fa-building fa-2x mb-2"></i>
                  <br>
                  No facilities found
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer bg-white" *ngIf="totalFacilities > pageSize">
          <div class="d-flex justify-content-center">
            <pagination
              [(ngModel)]="currentPage"
              [totalItems]="totalFacilities"
              [itemsPerPage]="pageSize"
              [maxSize]="5"
              [rotate]="false"
              [boundaryLinks]="true"
              (pageChanged)="onPageChange($event.page)">
            </pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- Facility Form Section -->
    <div class="col-lg-4">
      <div class="card shadow-sm">
        <div class="card-header bg-white border-bottom">
          <h5 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>
            {{ isEditing ? 'Edit' : 'Add' }} Facility
          </h5>
        </div>
        <div class="card-body">
          <form (ngSubmit)="saveFacility()" #facilityForm="ngForm">
            <!-- Facility Number -->
            <div class="mb-3">
              <label class="form-label fw-bold">Facility Number *</label>
              <input type="text" required #facilityNo="ngModel"
                     [class.is-invalid]="facilityNo.invalid && facilityNo.touched"
                     class="form-control" id="facilityNo"
                     [(ngModel)]="facility.facilityNo" name="facilityNo"
                     placeholder="Enter facility number" autocomplete="off">
              <div class="invalid-feedback">
                Facility number is required.
              </div>
            </div>

            <!-- Facility Name -->
            <div class="mb-3">
              <label class="form-label fw-bold">Facility Name *</label>
              <input type="text" required #facilityName="ngModel"
                     [class.is-invalid]="facilityName.invalid && facilityName.touched"
                     class="form-control" id="facilityName"
                     [(ngModel)]="facility.facilityName" name="facilityName"
                     placeholder="Enter facility name" autocomplete="off">
              <div class="invalid-feedback">
                Facility name is required.
              </div>
            </div>

            <!-- Facility Type -->
            <div class="mb-3">
              <label class="form-label fw-bold">Facility Type *</label>
              <select class="form-select" required #facilityType="ngModel"
                      [class.is-invalid]="facilityType.invalid && facilityType.touched"
                      [(ngModel)]="selectedFacilityType" name="facilityType">
                <option [ngValue]="null">Select facility type</option>
                <option *ngFor="let type of facilityTypes" [ngValue]="type">
                  {{ type.name }}
                </option>
              </select>
              <div class="invalid-feedback">
                Please select a facility type.
              </div>
            </div>

            <!-- Facility Charge -->
            <div class="mb-3">
              <label class="form-label fw-bold">Facility Charge</label>
              <input type="number" #facilityCharge="ngModel"
                     class="form-control" id="facilityCharge"
                     [(ngModel)]="facility.facilityCharge" name="facilityCharge"
                     placeholder="0.00" min="0" step="0.01">
            </div>

            <!-- Active Status -->
            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="isActive"
                       [(ngModel)]="isActive" name="isActive">
                <label class="form-check-label fw-bold" for="isActive">
                  Active
                </label>
              </div>
            </div>

            <!-- Form Buttons -->
            <div class="d-flex justify-content-end gap-2">
              <button type="button" class="btn btn-warning" (click)="clearForm()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit" class="btn btn-primary"
                      [disabled]="!facilityForm.form.valid">
                <i class="fas fa-save me-1"></i>
                {{ isEditing ? 'Update' : 'Save' }} Facility
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
