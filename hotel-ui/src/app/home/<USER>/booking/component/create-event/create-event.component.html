<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="mb-0 text-primary fw-bold pb-2">CREATE EVENT</h2>
    <button *ngIf="isModal" type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <form (ngSubmit)="saveEvent()" #createEventForm="ngForm" class="needs-validation">
    <!-- Basic Event Information -->
    <div class="card mb-4">
      <div class="card-header bg-light">
        <h6 class="mb-0 text-primary"><i class="fas fa-info-circle me-2"></i>Event Information</h6>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <!-- Event Name -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Event Name *</label>
            <input type="text" required #eventName="ngModel"
                   [class.is-invalid]="eventName.invalid && eventName.touched"
                   class="form-control" id="eventName"
                   [(ngModel)]="event.eventName" name="eventName"
                   placeholder="Enter event name" autocomplete="off">
            <div class="invalid-feedback">
              Event name is required.
            </div>
          </div>

          <!-- Event Code -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Event Code</label>
            <input type="text" #eventCode="ngModel"
                   class="form-control" id="eventCode"
                   [(ngModel)]="event.eventCode" name="eventCode"
                   placeholder="Auto-generated if empty" autocomplete="off">
          </div>

          <!-- Event Date -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Event Date *</label>
            <input type="date" required #eventDate="ngModel"
                   [class.is-invalid]="eventDate.invalid && eventDate.touched"
                   class="form-control" id="eventDate"
                   [(ngModel)]="event.eventDate" name="eventDate">
            <div class="invalid-feedback">
              Event date is required.
            </div>
          </div>

          <!-- Number of Participants -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Number of Participants *</label>
            <input type="number" required #participants="ngModel"
                   [class.is-invalid]="participants.invalid && participants.touched"
                   class="form-control" id="participants"
                   [(ngModel)]="event.numberOfParticipants" name="participants"
                   placeholder="0" min="1">
            <div class="invalid-feedback">
              Number of participants is required.
            </div>
          </div>

          <!-- Venue Selection -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Venue *</label>
            <input type="text" class="form-control" required #venue="ngModel"
                   [class.is-invalid]="venue.invalid && venue.touched"
                   [(ngModel)]="keyVenueSearch"
                   [typeahead]="venues"
                   (typeaheadLoading)="loadVenues()"
                   (typeaheadOnSelect)="setSelectedVenue($event)"
                   typeaheadOptionField="facilityName"
                   placeholder="Search and select venue"
                   name="venue"
                   autocomplete="off">
            <small class="text-muted" *ngIf="selectedVenue">
              Selected: {{ selectedVenue.facilityName }} - {{ selectedVenue.facilityCharge | currency:'LKR':'symbol':'1.2-2' }}
            </small>
            <div class="invalid-feedback">
              Please select a venue.
            </div>
          </div>

          <!-- Customer Selection -->
          <div class="col-12 col-sm-6 col-md-4">
            <label class="form-label fw-bold">Customer *</label>
            <input type="text" class="form-control" required #customer="ngModel"
                   [class.is-invalid]="customer.invalid && customer.touched"
                   [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customers"
                   (typeaheadLoading)="loadCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   typeaheadOptionField="name"
                   placeholder="Search and select customer"
                   name="customer"
                   autocomplete="off">
            <small class="text-muted" *ngIf="selectedCustomer">
              Selected: {{ selectedCustomer.name }}
            </small>
            <div class="invalid-feedback">
              Please select a customer.
            </div>
          </div>

          <!-- Description -->
          <div class="col-12">
            <label class="form-label fw-bold">Description</label>
            <textarea class="form-control" id="description"
                      [(ngModel)]="event.description" name="description"
                      rows="3" placeholder="Enter event description"></textarea>
          </div>

          <!-- Active Status -->
          <div class="col-12">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="isActive"
                     [(ngModel)]="isActive" name="isActive">
              <label class="form-check-label fw-bold" for="isActive">
                Active
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Items Section -->
    <div class="card mb-4">
      <div class="card-header bg-light">
        <h6 class="mb-0 text-primary"><i class="fas fa-list me-2"></i>Event Items</h6>
      </div>
      <div class="card-body">
        <!-- Add Event Item Form -->
        <div class="row g-3 mb-3">
          <div class="col-md-4">
            <label class="form-label fw-bold">Event Item</label>
            <input type="text" class="form-control"
                   [(ngModel)]="keyEventItemSearch" (typeaheadLoading)="loadEventItems()"
                   [typeahead]="eventItems"
                   (typeaheadOnSelect)="setSelectedEventItem($event)"
                   typeaheadOptionField="itemName"
                   placeholder="Search event item"
                   name="eventItemSearch"
                   autocomplete="off">
            <small class="text-muted" *ngIf="selectedEventItem">
              Selected: {{ selectedEventItem.itemName }}
            </small>
          </div>
          <div class="col-md-3">
            <label class="form-label fw-bold">Quantity</label>
            <input type="number" class="form-control" [(ngModel)]="eventItemQuantity"
                   name="quantity" min="1" step="1" placeholder="1">
          </div>
          <div class="col-md-3">
            <label class="form-label fw-bold">Unit Price</label>
            <input type="number" class="form-control" [(ngModel)]="eventItemUnitPrice"
                   name="unitPrice" min="0" step="0.01" placeholder="0.00">
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <button type="button" class="btn btn-success w-100" (click)="addEventItem()">
              <i class="fas fa-plus me-1"></i>Add
            </button>
          </div>
        </div>

        <!-- Event Items Table -->
        <div class="table-responsive" style="min-height: 200px;">
          <table class="table table-hover table-striped">
            <thead class="table-light">
              <tr>
                <th>Item Name</th>
                <th class="text-center">Quantity</th>
                <th class="text-end">Unit Price</th>
                <th class="text-end">Total Price</th>
                <th class="text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let eventItem of event.eventItems; let i = index">
                <td class="align-middle">{{ eventItem.item.itemName }}</td>
                <td class="text-center">
                  <input type="number" class="form-control form-control-sm text-center"
                         [(ngModel)]="eventItem.quantity"
                         (ngModelChange)="updateEventItemQuantity(i)"
                         min="1" step="1" style="width: 80px;">
                </td>
                <td class="text-end">
                  <input type="number" class="form-control form-control-sm text-end"
                         [(ngModel)]="eventItem.unitPrice"
                         (ngModelChange)="updateEventItemUnitPrice(i)"
                         min="0" step="0.01" style="width: 100px;">
                </td>
                <td class="text-end align-middle">
                  {{ eventItem.totalPrice | currency:'LKR':'symbol':'1.2-2' }}
                </td>
                <td class="text-center">
                  <button type="button" class="btn btn-outline-danger btn-sm"
                          (click)="removeEventItem(i)">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
              <tr *ngIf="event.eventItems.length === 0">
                <td colspan="5" class="text-center text-muted py-3">
                  <i class="fas fa-info-circle me-2"></i>No event items added yet
                </td>
              </tr>
            </tbody>
            <tfoot *ngIf="event.eventItems.length > 0" class="table-light">
              <tr>
                <th colspan="3" class="text-end">Total Amount:</th>
                <th class="text-end">{{ event.totalAmount | currency:'LKR':'symbol':'1.2-2' }}</th>
                <th></th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- Form Buttons -->
    <div class="row mt-4">
      <div class="col-12 d-flex justify-content-end">
        <button type="button" class="btn btn-warning me-2" (click)="clearForm()">Clear</button>
        <button type="submit" class="btn btn-primary"
                [disabled]="!createEventForm.form.valid">
          {{ isEditing ? 'Update' : 'Save' }} Event
        </button>
      </div>
    </div>
  </form>
</div>
