<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Accounts Payable Report</h2>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body text-center">
          <h6>Total Outstanding</h6>
          <h4>{{ formatCurrency(totalOutstanding) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-danger text-white">
        <div class="card-body text-center">
          <h6>Overdue Amount</h6>
          <h4>{{ formatCurrency(totalOverdue) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body text-center">
          <h6>Current Amount</h6>
          <h4>{{ formatCurrency(totalCurrent) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body text-center">
          <h6>Total Records</h6>
          <h4>{{ filteredData.length }}</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Filters</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">Supplier</label>
              <input type="text"
                     class="form-control"
                     [(ngModel)]="supplierFilter"
                     (input)="applyFilters()"
                     placeholder="Search supplier...">
            </div>
            <div class="col-md-2">
              <label class="form-label">Status</label>
              <select class="form-select"
                      [(ngModel)]="statusFilter"
                      (change)="applyFilters()">
                <option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">From Date</label>
              <input type="date"
                     class="form-control"
                     [(ngModel)]="fromDate"
                     (change)="applyFilters()">
            </div>
            <div class="col-md-2">
              <label class="form-label">To Date</label>
              <input type="date"
                     class="form-control"
                     [(ngModel)]="toDate"
                     (change)="applyFilters()">
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button class="btn btn-outline-secondary me-2" (click)="clearFilters()">
                <i class="fas fa-times"></i> Clear Filters
              </button>
              <button class="btn btn-primary me-2" (click)="printReport()">
                <i class="fas fa-print"></i> Print
              </button>
              <button class="btn btn-success" (click)="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Pending Purchase Invoices</h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Invoice No</th>
                  <th>Supplier</th>
                  <th>Invoice Date</th>
                  <th>Due Date</th>
                  <th>Invoice Amount</th>
                  <th>Outstanding Amount</th>
                  <th>Status</th>
                  <th>Days Overdue</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of filteredData" [class.table-danger]="isOverdue(item.dueDate)">
                  <td>{{ item.invoiceNo || 'N/A' }}</td>
                  <td>{{ item.supplier?.name || 'N/A' }}</td>
                  <td>{{ formatDate(item.date) }}</td>
                  <td>{{ formatDate(item.dueDate) }}</td>
                  <td>{{ formatCurrency(item.totalAmount) }}</td>
                  <td>{{ formatCurrency(item.balance) }}</td>
                  <td>
                    <span class="badge" [class]="getStatusClass(item.status)">
                      {{ item.status?.value || 'N/A' }}
                    </span>
                  </td>
                  <td>
                    <span *ngIf="isOverdue(item.dueDate)" class="text-danger">
                      {{ calculateDaysOverdue(item.dueDate) }}
                    </span>
                    <span *ngIf="!isOverdue(item.dueDate)" class="text-muted">-</span>
                  </td>
                </tr>
                <tr *ngIf="loading">
                  <td colspan="8" class="text-center py-4">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                  </td>
                </tr>
                <tr *ngIf="!loading && filteredData.length === 0">
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No accounts payable records found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
