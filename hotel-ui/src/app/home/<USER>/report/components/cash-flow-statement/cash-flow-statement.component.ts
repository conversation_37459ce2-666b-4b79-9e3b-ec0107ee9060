import { Component, OnInit } from '@angular/core';
import { SalesInvoiceService } from '../../../trade/service/sales-invoice.service';
import { PurchaseInvoiceService } from '../../../trade/service/purchase-invoice.service';
import { ExpenseService } from '../../../trade/service/expense.service';

@Component({
  standalone:false,
  selector: 'app-cash-flow-statement',
  templateUrl: './cash-flow-statement.component.html',
  styleUrls: ['./cash-flow-statement.component.css']
})
export class CashFlowStatementComponent implements OnInit {

  // Date filters
  fromDate: Date = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  toDate: Date = new Date();

  // Cash flows
  operatingActivities = {
    cashFromSales: 0,
    cashFromCustomers: 0,
    cashPaidToSuppliers: 0,
    cashPaidForExpenses: 0,
    netOperatingCashFlow: 0
  };

  investingActivities = {
    assetPurchases: 0,
    assetSales: 0,
    netInvestingCashFlow: 0
  };

  financingActivities = {
    ownerInvestments: 0,
    ownerWithdrawals: 0,
    loanProceeds: 0,
    loanRepayments: 0,
    netFinancingCashFlow: 0
  };

  // Summary
  netCashFlow = 0;
  beginningCash = 0;
  endingCash = 0;
  loading = true;

  constructor(
    private salesInvoiceService: SalesInvoiceService,
    private purchaseInvoiceService: PurchaseInvoiceService,
    private expenseService: ExpenseService
  ) { }

  ngOnInit(): void {
    this.loadCashFlowData();
  }

  loadCashFlowData(): void {
    this.loading = true;

    Promise.all([
      this.loadOperatingActivities(),
      this.loadInvestingActivities(),
      this.loadFinancingActivities()
    ]).then(() => {
      this.calculateNetCashFlows();
      this.loading = false;
    });
  }

  async loadOperatingActivities(): Promise<void> {
    try {
      // For now, initialize with zero values - will be populated when service methods are available
      this.operatingActivities.cashFromSales = 0;
      this.operatingActivities.cashPaidToSuppliers = 0;
      this.operatingActivities.cashPaidForExpenses = 0;

      // TODO: Implement when service methods are available
      console.log('Cash flow service methods not yet implemented');
    } catch (error) {
      console.error('Error loading operating activities:', error);
    }
  }

  isInDateRange(date: any): boolean {
    if (!date) return false;
    const checkDate = new Date(date);
    return checkDate >= this.fromDate && checkDate <= this.toDate;
  }

  async loadInvestingActivities(): Promise<void> {
    try {
      // This would load asset purchases and sales
      // For now, setting to 0 as we don't have specific asset transaction data
      this.investingActivities.assetPurchases = 0;
      this.investingActivities.assetSales = 0;
    } catch (error) {
      console.error('Error loading investing activities:', error);
    }
  }

  async loadFinancingActivities(): Promise<void> {
    try {
      // This would load equity and liability transactions
      // For now, setting to 0 as we don't have specific financing transaction data
      this.financingActivities.ownerInvestments = 0;
      this.financingActivities.ownerWithdrawals = 0;
      this.financingActivities.loanProceeds = 0;
      this.financingActivities.loanRepayments = 0;
    } catch (error) {
      console.error('Error loading financing activities:', error);
    }
  }

  calculateNetCashFlows(): void {
    // Operating activities net cash flow
    this.operatingActivities.netOperatingCashFlow =
      this.operatingActivities.cashFromSales +
      this.operatingActivities.cashFromCustomers -
      this.operatingActivities.cashPaidToSuppliers -
      this.operatingActivities.cashPaidForExpenses;

    // Investing activities net cash flow
    this.investingActivities.netInvestingCashFlow =
      this.investingActivities.assetSales -
      this.investingActivities.assetPurchases;

    // Financing activities net cash flow
    this.financingActivities.netFinancingCashFlow =
      this.financingActivities.ownerInvestments +
      this.financingActivities.loanProceeds -
      this.financingActivities.ownerWithdrawals -
      this.financingActivities.loanRepayments;

    // Net cash flow
    this.netCashFlow =
      this.operatingActivities.netOperatingCashFlow +
      this.investingActivities.netInvestingCashFlow +
      this.financingActivities.netFinancingCashFlow;

    // Ending cash (beginning cash + net cash flow)
    this.endingCash = this.beginningCash + this.netCashFlow;
  }

  onDateChange(): void {
    this.loadCashFlowData();
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: Date): string {
    return date ? date.toLocaleDateString() : '';
  }

  printReport(): void {
    window.print();
  }

  exportToExcel(): void {
    // TODO: Implement Excel export
    console.log('Excel export functionality to be implemented');
  }
}
