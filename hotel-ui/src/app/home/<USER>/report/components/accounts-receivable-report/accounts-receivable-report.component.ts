import { Component, OnInit } from '@angular/core';
import { SalesInvoiceService } from '../../../trade/service/sales-invoice.service';
import { CustomerService } from '../../../trade/service/customer.service';
import { AccountsReceivableService } from '../../../accounting/service/accounts-receivable.service';

@Component({
  standalone: false,
  selector: 'app-accounts-receivable-report',
  templateUrl: './accounts-receivable-report.component.html',
  styleUrls: ['./accounts-receivable-report.component.css']
})
export class AccountsReceivableReportComponent implements OnInit {

  sis: any[] = [];
  arData: any[] = [];
  filteredData: any[] = [];
  loading = true;

  // Filters
  keyCustomerSearch: string = '';
  keyCustomer: string = '';
  customerFilter = '';
  statusFilter = 'ALL';
  fromDate: Date = null;
  toDate: Date = null;
  customerSearchList: any[] = [];

  // Summary
  totalAmount = 0;
  totalOutstanding = 0;
  totalOverdue = 0;
  totalCurrent = 0;

  // Status options
  statusOptions = [
    { value: 'ALL', label: 'All Status' },
    { value: 'OUTSTANDING', label: 'Outstanding' },
    { value: 'OVERDUE', label: 'Overdue' },
    { value: 'PAID', label: 'Paid' }
  ];

  constructor(
    private salesInvoiceService: SalesInvoiceService,
    private customerService: CustomerService,
    private accountsReceivableService: AccountsReceivableService
  ) { }

  ngOnInit(): void {
    this.loadCreditData();
    this.loadCustomers();
  }

  loadCreditData(): void {
    this.loading = true;

    // Use the accounts receivable service to get outstanding invoices
    this.accountsReceivableService.getOutstandingInvoices().subscribe(
      (data: any[]) => {
        console.log('Outstanding sales invoices:', data);
        this.sis = data || [];
        this.arData = [...this.sis];
        this.filteredData = [...this.sis];
        this.calculateSummary();
        this.loading = false;
      },
      (error) => {
        console.error('Error loading outstanding sales invoices:', error);
        this.loading = false;
        // Show empty state with error message
        this.sis = [];
        this.arData = [];
        this.filteredData = [];
        this.calculateSummary();
      }
    );
  }

  loadCustomers(): void {
    this.customerService.findAllPagination(0, 1000).subscribe(
      (data: any) => {
        this.customerSearchList = data.content || data || [];
      },
      (error) => {
        console.error('Error loading customers:', error);
      }
    );
  }

  applyFilters(): void {
    this.filteredData = this.arData.filter(item => {
      let matches = true;

      // Customer filter
      if (this.customerFilter && this.customerFilter.trim() !== '') {
        matches = matches && item.customer?.name?.toLowerCase().includes(this.customerFilter.toLowerCase());
      }

      // Status filter
      if (this.statusFilter !== 'ALL') {
        matches = matches && item.status === this.statusFilter;
      }

      // Date range filter
      if (this.fromDate) {
        matches = matches && new Date(item.invoiceDate) >= this.fromDate;
      }
      if (this.toDate) {
        matches = matches && new Date(item.invoiceDate) <= this.toDate;
      }

      return matches;
    });

    this.calculateSummary();
  }

  calculateSummary(): void {
    this.totalOutstanding = this.filteredData.reduce((sum, item) => sum + (item.balance || 0), 0);

    this.totalOverdue = this.filteredData
      .filter(item => item.status?.value === 'OVERDUE' || this.isOverdue(item.dueDate))
      .reduce((sum, item) => sum + (item.balance || 0), 0);

    this.totalCurrent = this.filteredData
      .filter(item => (item.status?.value === 'OUTSTANDING' || item.status?.value === 'PENDING') && !this.isOverdue(item.dueDate))
      .reduce((sum, item) => sum + (item.balance || 0), 0);
  }

  isOverdue(dueDate: Date): boolean {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  }

  clearFilters(): void {
    this.customerFilter = '';
    this.statusFilter = 'ALL';
    this.fromDate = null;
    this.toDate = null;
    this.applyFilters();
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  }

  formatCurrency(amount: number): string {
    if (amount == null || amount == undefined) return '0.00';
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  getStatusClass(status: any): string {
    const statusValue = status?.value || status;
    if (!statusValue) return 'badge-secondary';

    switch (statusValue.toLowerCase()) {
      case 'outstanding':
      case 'pending':
        return 'badge-warning';
      case 'overdue':
        return 'badge-danger';
      case 'paid':
      case 'completed':
        return 'badge-success';
      case 'cancelled':
        return 'badge-danger';
      default:
        return 'badge-secondary';
    }
  }

  printReport(): void {
    window.print();
  }

  calculateDaysOverdue(dueDate: Date): number {
    if (!dueDate) return 0;
    const now = new Date();
    const due = new Date(dueDate);
    const diffTime = now.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  }

  exportToExcel(): void {
    // TODO: Implement Excel export
    console.log('Excel export functionality to be implemented');
  }
}
