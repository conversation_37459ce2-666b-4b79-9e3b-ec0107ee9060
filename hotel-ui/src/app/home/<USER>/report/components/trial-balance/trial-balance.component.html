<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-dark">Trial Balance</h2>
        <div class="btn-group">
          <button class="btn btn-outline-primary btn-sm" (click)="printReport()">
            <i class="fa fa-print"></i> Print
          </button>
          <button class="btn btn-outline-success btn-sm" (click)="exportToExcel()">
            <i class="fa fa-file-excel"></i> Excel
          </button>
          <button class="btn btn-outline-danger btn-sm" (click)="exportToPDF()">
            <i class="fa fa-file-pdf"></i> PDF
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="loading">
    <div class="col-12 text-center">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="!loading">
    <div class="col-12">
      <div class="card">
        <div class="card-header text-center">
          <h4 class="mb-1">Trial Balance</h4>
          <p class="mb-0 text-muted">As of {{ formatDate(reportDate) }}</p>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="thead-light">
                <tr>
                  <th>Account Name</th>
                  <th>Account Type</th>
                  <th class="text-right">Debit Balance</th>
                  <th class="text-right">Credit Balance</th>
                </tr>
              </thead>
              <tbody>
                <!-- Assets -->
                <tr class="table-info">
                  <td colspan="4"><strong>ASSETS</strong></td>
                </tr>
                <tr *ngFor="let account of getAccountsByType('Asset')">
                  <td class="pl-4">{{ account.accountName }}</td>
                  <td>{{ account.accountType }}</td>
                  <td class="text-right">
                    <span *ngIf="account.debitBalance > 0">{{ formatCurrency(account.debitBalance) }}</span>
                    <span *ngIf="account.debitBalance === 0">-</span>
                  </td>
                  <td class="text-right">
                    <span *ngIf="account.creditBalance > 0">{{ formatCurrency(account.creditBalance) }}</span>
                    <span *ngIf="account.creditBalance === 0">-</span>
                  </td>
                </tr>

                <!-- Liabilities -->
                <tr class="table-warning">
                  <td colspan="4"><strong>LIABILITIES</strong></td>
                </tr>
                <tr *ngFor="let account of getAccountsByType('Liability')">
                  <td class="pl-4">{{ account.accountName }}</td>
                  <td>{{ account.accountType }}</td>
                  <td class="text-right">
                    <span *ngIf="account.debitBalance > 0">{{ formatCurrency(account.debitBalance) }}</span>
                    <span *ngIf="account.debitBalance === 0">-</span>
                  </td>
                  <td class="text-right">
                    <span *ngIf="account.creditBalance > 0">{{ formatCurrency(account.creditBalance) }}</span>
                    <span *ngIf="account.creditBalance === 0">-</span>
                  </td>
                </tr>

                <!-- Equity -->
                <tr class="table-success">
                  <td colspan="4"><strong>EQUITY</strong></td>
                </tr>
                <tr *ngFor="let account of getAccountsByType('Equity')">
                  <td class="pl-4">{{ account.accountName }}</td>
                  <td>{{ account.accountType }}</td>
                  <td class="text-right">
                    <span *ngIf="account.debitBalance > 0">{{ formatCurrency(account.debitBalance) }}</span>
                    <span *ngIf="account.debitBalance === 0">-</span>
                  </td>
                  <td class="text-right">
                    <span *ngIf="account.creditBalance > 0">{{ formatCurrency(account.creditBalance) }}</span>
                    <span *ngIf="account.creditBalance === 0">-</span>
                  </td>
                </tr>

                <!-- Revenue -->
                <tr class="table-primary">
                  <td colspan="4"><strong>REVENUE</strong></td>
                </tr>
                <tr *ngFor="let account of getAccountsByType('Revenue')">
                  <td class="pl-4">{{ account.accountName }}</td>
                  <td>{{ account.accountType }}</td>
                  <td class="text-right">
                    <span *ngIf="account.debitBalance > 0">{{ formatCurrency(account.debitBalance) }}</span>
                    <span *ngIf="account.debitBalance === 0">-</span>
                  </td>
                  <td class="text-right">
                    <span *ngIf="account.creditBalance > 0">{{ formatCurrency(account.creditBalance) }}</span>
                    <span *ngIf="account.creditBalance === 0">-</span>
                  </td>
                </tr>

                <!-- Expenses -->
                <tr class="table-danger">
                  <td colspan="4"><strong>EXPENSES</strong></td>
                </tr>
                <tr *ngFor="let account of getAccountsByType('Expense')">
                  <td class="pl-4">{{ account.accountName }}</td>
                  <td>{{ account.accountType }}</td>
                  <td class="text-right">
                    <span *ngIf="account.debitBalance > 0">{{ formatCurrency(account.debitBalance) }}</span>
                    <span *ngIf="account.debitBalance === 0">-</span>
                  </td>
                  <td class="text-right">
                    <span *ngIf="account.creditBalance > 0">{{ formatCurrency(account.creditBalance) }}</span>
                    <span *ngIf="account.creditBalance === 0">-</span>
                  </td>
                </tr>

                <!-- Totals -->
                <tr class="table-dark font-weight-bold">
                  <td colspan="2"><strong>TOTALS</strong></td>
                  <td class="text-right"><strong>{{ formatCurrency(totalDebits) }}</strong></td>
                  <td class="text-right"><strong>{{ formatCurrency(totalCredits) }}</strong></td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Balance Check -->
          <div class="row mt-4">
            <div class="col-12">
              <div class="alert"
                   [class.alert-success]="isBalanced()"
                   [class.alert-danger]="!isBalanced()">
                <strong>Balance Check:</strong>
                <span *ngIf="isBalanced()">
                  ✓ Trial Balance is balanced (Total Debits = Total Credits)
                </span>
                <span *ngIf="!isBalanced()">
                  ✗ Trial Balance is not balanced. Please check the figures.
                  Difference: {{ formatCurrency(Math.abs((totalDebits || 0) - (totalCredits || 0))) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Summary Statistics -->
          <div class="row mt-4">
            <div class="col-md-3">
              <div class="card bg-info text-white">
                <div class="card-body text-center">
                  <h6>Total Accounts</h6>
                  <h4>{{ accounts.length }}</h4>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-primary text-white">
                <div class="card-body text-center">
                  <h6>Total Debits</h6>
                  <h4>{{ formatCurrency(totalDebits) }}</h4>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                  <h6>Total Credits</h6>
                  <h4>{{ formatCurrency(totalCredits) }}</h4>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card" [class.bg-success]="isBalanced()" [class.bg-danger]="!isBalanced()" class="text-white">
                <div class="card-body text-center">
                  <h6>Difference</h6>
                  <h4>{{ formatCurrency(Math.abs((totalDebits || 0) - (totalCredits || 0))) }}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
