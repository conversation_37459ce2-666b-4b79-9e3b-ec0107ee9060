import { Component, OnInit } from '@angular/core';
import { SupplierReturnService } from '../../../inventory/service/supplier-return.service';
import { SupplierService } from '../../../trade/service/supplier.service';
import { ItemService } from '../../../inventory/service/item.service';
import { StockService } from '../../../inventory/service/stock.service';
import { NotificationService } from '../../../../core/service/notification.service';
import { SupplierReturn } from '../../../inventory/model/supplier-return';
import { Supplier } from '../../../trade/model/supplier';
import { Item } from '../../../inventory/model/item';
import { Stock } from '../../../inventory/model/stock';

@Component({
standalone: false,
  selector: 'app-supplier-return-report',
  templateUrl: './supplier-return-report.component.html',
  styleUrls: ['./supplier-return-report.component.css']
})
export class SupplierReturnReportComponent implements OnInit {

  // Search fields
  keyItemName: string;
  keySupplier: string;

  // Search results for typeahead
  itemSearchResults: Item[] = [];
  supplierSearchResults: Supplier[] = [];

  // Selected supplier
  selectedSupplier: Supplier = null;

  // Return items array
  returnItems: any[] = [];

  // UI state
  loading = false;

  // Return reasons
  returnReasons = [
    'Physically Damaged',
    'Expired',
    'Not Working',
    'Wrong Item',
    'Quality Issue',
    'Overstock',
    'Other'
  ];

  constructor(
    private supplierReturnService: SupplierReturnService,
    private supplierService: SupplierService,
    private itemService: ItemService,
    private stockService: StockService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    // Initialize component
  }

  /**
   * Load items for barcode/name search
   */
  loadItems(): void {
    if (!this.keyItemName || this.keyItemName.length < 2) {
      this.itemSearchResults = [];
      return;
    }

    // Search by both barcode and name
    this.itemService.findAllByBarcodeLike(this.keyItemName).subscribe(
      (barcodeResults: Item[]) => {
        this.itemService.findAllActiveByNameLike(this.keyItemName).subscribe(
          (nameResults: Item[]) => {
            // Combine results
            const combinedResults = [...barcodeResults];
            nameResults.forEach(nameItem => {
              if (!combinedResults.find(item => item.id === nameItem.id)) {
                combinedResults.push(nameItem);
              }
            });
            this.itemSearchResults = combinedResults.slice(0, 15);
          },
          error => {
            this.itemSearchResults = barcodeResults.slice(0, 15);
          }
        );
      },
      error => {
        this.itemService.findAllActiveByNameLike(this.keyItemName).subscribe(
          (nameResults: Item[]) => {
            this.itemSearchResults = nameResults.slice(0, 15);
          },
          nameError => {
            this.itemSearchResults = [];
          }
        );
      }
    );
  }

  /**
   * Load suppliers for search
   */
  loadSuppliers(): void {
    if (!this.keySupplier || this.keySupplier.length < 2) {
      this.supplierSearchResults = [];
      return;
    }

    this.supplierService.findByNameLike(this.keySupplier).subscribe(
      (data: Supplier[]) => {
        this.supplierSearchResults = data;
      },
      error => {
        console.error('Error loading suppliers:', error);
        this.supplierSearchResults = [];
      }
    );
  }

  /**
   * Add item to return list
   */
  addItemToReturn(event: any): void {
    const selectedItem = event.item;
    this.keyItemName = '';

    // Check if item already exists in return list
    const existingItem = this.returnItems.find(item => item.barcode === selectedItem.barcode);
    if (existingItem) {
      this.notificationService.showWarning('Item already added to return list');
      return;
    }

    // Get stock information for the item
    this.loading = true;
    this.stockService.searchByWhAndBarcodeLike(selectedItem.barcode).subscribe(
      (stockData: any) => {
        if (stockData && stockData.length > 0) {
          const stock = stockData[0];

          // Add item to return list
          const returnItem = {
            itemCode: selectedItem.itemCode,
            itemName: selectedItem.itemName,
            barcode: selectedItem.barcode,
            currentStock: stock.quantity || 0,
            returnQuantity: 1,
            sellingPrice: stock.sellingPrice || 0,
            itemCost: stock.itemCost || 0,
            totalCost: stock.itemCost || 0,
            reason: '',
            stockId: stock.id,
            warehouseCode: stock.warehouseCode,
            warehouseName: stock.warehouseName
          };

          this.returnItems.push(returnItem);
          this.updateItemTotal(this.returnItems.length - 1);
          this.notificationService.showSuccess('Item added to return list');
        } else {
          this.notificationService.showError('No stock found for this item');
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading stock:', error);
        this.notificationService.showError('Error loading stock information');
        this.loading = false;
      }
    );
  }

  /**
   * Set selected supplier from typeahead
   */
  setSelectedSupplier(event: any): void {
    this.selectedSupplier = event.item;
    this.keySupplier = '';
  }

  /**
   * Update item total cost when quantity changes
   */
  updateItemTotal(index: number): void {
    const item = this.returnItems[index];
    item.totalCost = item.returnQuantity * item.itemCost;
  }

  /**
   * Remove item from return list
   */
  removeItem(index: number): void {
    this.returnItems.splice(index, 1);
    this.notificationService.showSuccess('Item removed from return list');
  }

  /**
   * Get total quantity of all return items
   */
  getTotalQuantity(): number {
    return this.returnItems.reduce((sum, item) => sum + (item.returnQuantity || 0), 0);
  }

  /**
   * Get total cost of all return items
   */
  getTotalCost(): number {
    return this.returnItems.reduce((sum, item) => sum + (item.totalCost || 0), 0);
  }

  /**
   * Check if return can be processed
   */
  canProcessReturn(): boolean {
    return this.returnItems.length > 0 &&
           this.selectedSupplier !== null &&
           this.returnItems.every(item =>
             item.returnQuantity > 0 &&
             item.returnQuantity <= item.currentStock &&
             item.reason && item.reason.trim() !== ''
           );
  }

  /**
   * Clear all items from return list
   */
  clearAll(): void {
    this.returnItems = [];
    this.notificationService.showSuccess('All items cleared from return list');
  }

  /**
   * Process the supplier return
   */
  processReturn(): void {
    if (!this.canProcessReturn()) {
      this.notificationService.showError('Please ensure all items have valid quantities and reasons, and a supplier is selected');
      return;
    }

    this.loading = true;

    // Process each item as a separate return
    const returnPromises = this.returnItems.map(item => {
      const supplierReturn = new SupplierReturn();
      supplierReturn.itemCode = item.itemCode;
      supplierReturn.barcode = item.barcode;
      supplierReturn.itemName = item.itemName;
      supplierReturn.supplierCode = this.selectedSupplier.supplierNo;
      supplierReturn.supplierName = this.selectedSupplier.name;
      supplierReturn.quantity = item.returnQuantity;
      supplierReturn.sellingPrice = item.sellingPrice;
      supplierReturn.itemCost = item.itemCost;
      supplierReturn.reason = item.reason;
      supplierReturn.warehouseCode = item.warehouseCode;
      supplierReturn.warehouseName = item.warehouseName;

      return this.supplierReturnService.save(supplierReturn).toPromise();
    });

    Promise.all(returnPromises).then(
      (responses) => {
        const successCount = responses.filter(response => response.success).length;
        const failCount = responses.length - successCount;

        if (successCount > 0) {
          this.notificationService.showSuccess(`${successCount} item(s) returned successfully`);
        }
        if (failCount > 0) {
          this.notificationService.showError(`${failCount} item(s) failed to return`);
        }

        // Clear the return list if all were successful
        if (failCount === 0) {
          this.returnItems = [];
          this.selectedSupplier = null;
          this.keySupplier = '';
        }

        this.loading = false;
      },
      error => {
        console.error('Error processing returns:', error);
        this.notificationService.showError('Failed to process returns');
        this.loading = false;
      }
    );
  }
}
