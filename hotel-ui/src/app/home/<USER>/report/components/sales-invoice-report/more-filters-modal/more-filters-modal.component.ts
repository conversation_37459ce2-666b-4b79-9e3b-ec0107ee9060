import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CashDrawerService } from '../../../../trade/service/cashDrawer.service';
import { CashDrawer } from '../../../../trade/model/cashDrawer';
import { User } from "../../../../../admin/model/user";
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../../../admin/service/user.service';

@Component({
standalone: false,
  selector: 'app-more-filters-modal',
  templateUrl: './more-filters-modal.component.html',
  styleUrls: ['./more-filters-modal.component.css']
})
export class MoreFiltersModalComponent implements OnInit {
  // Form
  filterForm: FormGroup;

  // Data
  cashDrawers: CashDrawer[] = [];
  cashierUsers: User[] = [];
  // Loading state
  loading: boolean = false;

  // Selected values (for returning to parent component)
  selectedCashDrawer: CashDrawer | null = null; // This is a Cash Drawer (device)
  selectedCashierUser: User | null = null; // This is a User with cashier role
  // Date fields removed as they are already available in the main report interface

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private cashDrawerService: CashDrawerService,
    private userService: UserService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      cashDrawerNo: [null],
      cashierUserId: [null]
      // Date fields removed as they are already available in the main report interface
    });
  }

  ngOnInit(): void {
    this.loadCashiers();
  }

  /**
   * Load all cash drawers and users with cashier role
   */
  loadCashiers(): void {
    this.loading = true;

    // Load users with cashier role
    this.loadCashierUsers();

    // Load cash drawers
    this.loadCashDrawers();
  }

  /**
   * Load users with cashier role
   */
  loadCashierUsers(): void {
    this.userService.findUsersWithCashierRole().subscribe(
      (data: User[]) => {
        this.cashierUsers = data;

        // Check if loading is complete
        if (this.cashDrawers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        this.toastr.error('Failed to load users with cashier role', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Load all cash drawers
   */
  loadCashDrawers(): void {
    this.cashDrawerService.findAllCashDrawers().subscribe(
      (data: CashDrawer[]) => {
        this.cashDrawers = data;

        // Check if loading is complete
        if (this.cashierUsers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        this.toastr.error('Failed to load cash drawers', 'Error');
        this.loading = false;
      }
    );
  }



  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    const cashDrawerNo = this.filterForm.get('cashDrawerNo')?.value;
    const cashierUserName = this.filterForm.get('cashierUserId')?.value;

    // Date fields removed as they are already available in the main report interface

    // Find selected cash drawer
    if (cashDrawerNo) {
      this.selectedCashDrawer = this.cashDrawers.find(c => c.drawerNo === cashDrawerNo) || null;
      // If cash drawer is selected, clear user selection
      this.selectedCashierUser = null;
    } else {
      this.selectedCashDrawer = null;
    }

    // Find selected cashier user
    if (cashierUserName) {
      this.selectedCashierUser = this.cashierUsers.find(u => u.username === cashierUserName) || null;
      // If user is selected, clear cash drawer selection
      if (this.selectedCashierUser) {
        this.selectedCashDrawer = null;
      }
    } else {
      this.selectedCashierUser = null;
    }



    // Date fields removed as they are already available in the main report interface

    // Close modal with selected values
    this.modalRef.hide();
  }

  /**
   * Clear filters
   */
  clearFilters(): void {
    this.filterForm.reset();
    this.selectedCashDrawer = null;
    this.selectedCashierUser = null;

    // Date fields removed as they are already available in the main report interface
  }

  /**
   * Close the modal
   */
  closeModal(): void {
    this.modalRef.hide();
  }
}
