.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-top: none;
}

.table td {
  vertical-align: middle;
}

.table-active {
  background-color: #e3f2fd !important;
}

.table-success {
  background-color: #d4edda !important;
}

.table-warning {
  background-color: #fff3cd !important;
}

.font-weight-bold {
  font-weight: 600 !important;
}

.text-primary {
  color: #007bff !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-secondary {
  color: #6c757d !important;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

.btn-group .btn {
  margin-left: 0.25rem;
}

@media print {
  .btn-group {
    display: none !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .card-header {
    background-color: transparent !important;
    border-bottom: 2px solid #000 !important;
  }
}
