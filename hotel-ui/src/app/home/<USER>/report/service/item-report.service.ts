import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ReportApiConstants } from '../report-constants';
import { Item } from '../../inventory/model/item';

@Injectable({
  providedIn: 'root'
})
export class ItemReportService {

  constructor(private http: HttpClient) { }

  /**
   * Find all items with pagination
   * @param page Page number (0-based)
   * @param pageSize Number of items per page
   * @param sortBy Field to sort by
   * @param sortDirection Sort direction (asc/desc)
   * @returns Observable of paginated items
   */
  findAll(page: number, pageSize: number, sortBy: string = 'itemName', sortDirection: string = 'asc'): Observable<any> {
    return this.http.get(ReportApiConstants.ITEM_REPORT_FIND_ALL, {
      params: {
        page: page.toString(),
        pageSize: pageSize.toString(),
        sortBy: sortBy,
        sortDirection: sortDirection
      }
    });
  }

  /**
   * Find items with filters
   * @param page Page number (0-based)
   * @param pageSize Number of items per page
   * @param categoryId Category ID filter
   * @param brandId Brand ID filter
   * @param modelId Model ID filter
   * @param supplierId Supplier ID filter
   * @param wholesale Wholesale filter
   * @param retail Retail filter
   * @param manageStock Manage stock filter
   * @param active Active filter
   * @param sortBy Field to sort by
   * @param sortDirection Sort direction (asc/desc)
   * @returns Observable of filtered paginated items
   */
  findFiltered(
    page: number,
    pageSize: number,
    categoryId: string = null,
    brandId: string = null,
    modelId: string = null,
    supplierId: string = null,
    wholesale: boolean = null,
    retail: boolean = null,
    manageStock: boolean = null,
    active: boolean = null,
    sortBy: string = 'itemName',
    sortDirection: string = 'asc'
  ): Observable<any> {
    const params: any = {
      page: page.toString(),
      pageSize: pageSize.toString(),
      sortBy: sortBy,
      sortDirection: sortDirection
    };

    if (categoryId) params.categoryId = categoryId;
    if (brandId) params.brandId = brandId;
    if (modelId) params.modelId = modelId;
    if (supplierId) params.supplierId = supplierId;
    if (wholesale !== null) params.wholesale = wholesale.toString();
    if (retail !== null) params.retail = retail.toString();
    if (manageStock !== null) params.manageStock = manageStock.toString();
    if (active !== null) params.active = active.toString();

    return this.http.get(ReportApiConstants.ITEM_REPORT_FIND_FILTERED, { params });
  }

  /**
   * Export items to PDF
   * @param categoryId Category ID filter
   * @param brandId Brand ID filter
   * @param modelId Model ID filter
   * @param supplierId Supplier ID filter
   * @param manageStock Manage stock filter
   * @param active Active filter
   * @param sortBy Field to sort by
   * @param sortDirection Sort direction (asc/desc)
   * @returns Observable of PDF blob
   */
  exportToPdf(
    categoryId: string = null,
    brandId: string = null,
    modelId: string = null,
    supplierId: string = null,
    manageStock: boolean = null,
    active: boolean = null,
    sortBy: string = 'itemName',
    sortDirection: string = 'asc'
  ): Observable<any> {
    const params: any = {
      sortBy: sortBy,
      sortDirection: sortDirection
    };

    if (categoryId) params.categoryId = categoryId;
    if (brandId) params.brandId = brandId;
    if (modelId) params.modelId = modelId;
    if (supplierId) params.supplierId = supplierId;
    if (manageStock !== null) params.manageStock = manageStock.toString();
    if (active !== null) params.active = active.toString();

    return this.http.get(ReportApiConstants.ITEM_REPORT_EXPORT_TO_PDF, {
      params: params,
      responseType: 'blob',
      observe: 'response'
    });
  }
}
