import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
standalone: false,
  selector: 'app-p401',
  templateUrl: './401.component.html',
  styles: [`
    .fas { margin-right: 8px; }
    .btn { margin: 0 8px; }
    .display-1 { font-size: 6rem; }
  `]
})
export class P401Component implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
    // You could add analytics tracking for 401 errors here
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  goBack(): void {
    window.history.back();
  }
}
