import {Component, OnInit} from '@angular/core';
import {Transaction} from "../../../../core/model/transaction";
import {TransactionService} from "../../../../core/service/transaction.service";
import {SalesInvoice} from "../../model/sales-invoice";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
standalone: false,
  selector: 'app-payment-history',
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.css']
})
export class PaymentHistoryComponent implements OnInit {

  transactions: Array<Transaction> = [];
  invoice: SalesInvoice;
  modalRef: BsModalRef;
  isModal: boolean = false;

  constructor(private transactionService: TransactionService) {
  }

  ngOnInit(): void {
    this.invoice = new SalesInvoice();
    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  findAllTransactions() {
    this.transactionService.findByReference(this.invoice.invoiceNo).subscribe((result: Array<Transaction>) => {
      this.transactions = result;
    })
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

}
