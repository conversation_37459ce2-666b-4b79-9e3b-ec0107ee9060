<!-- Modal Structure -->
<div *ngIf="isModal" class="modal-body">
  <div class="card shadow-sm border-0">
    <div class="card-header bg-white border-bottom">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-user-plus me-2"></i>{{ isEdit ? 'Edit Customer' : 'New Customer' }}
        </h5>
        <button type="button" class="btn-close" (click)="modalRef.hide()" aria-label="Close"></button>
      </div>
    </div>
    <div class="card-body">
      <ng-container *ngTemplateOutlet="customerForm"></ng-container>
    </div>
  </div>
</div>

<!-- Regular Structure -->
<div *ngIf="!isModal" class="container-fluid mt-3">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="text-dark">{{ isEdit ? 'Edit Customer' : 'New Customer' }}</h2>
  </div>

  <div class="card shadow-sm">
    <div class="card-header bg-white border-bottom">
      <h5 class="card-title mb-0">
        <i class="fas fa-user me-2"></i>Customer Information
      </h5>
    </div>
    <div class="card-body">
      <ng-container *ngTemplateOutlet="customerForm"></ng-container>
    </div>
  </div>
</div>

<!-- Shared Form Template -->
<ng-template #customerForm>
  <form #newPersonForm="ngForm" (ngSubmit)="savePerson(newPersonForm);" class="form-professional">
              <div class="row g-3">

                <!-- Personal Information Section -->
                <div class="col-12">
                  <h6 class="section-title">
                    <i class="fas fa-user me-2"></i>Personal Information
                  </h6>
                  <hr class="section-divider">
                </div>

                <!-- Salutation -->
                <div class="col-md-3">
                  <label class="form-label fw-semibold">Salutation</label>
                  <select class="form-control"
                          [(ngModel)]="customer.salutation"
                          name="salutation">
                    <option value="">Select</option>
                    <option *ngFor="let sal of salutations" [value]="sal">{{ sal }}</option>
                  </select>
                </div>

                <!-- Full Name -->
                <div class="col-md-9">
                  <label class="form-label fw-semibold">Full Name *</label>
                  <input type="text"
                         required
                         #customerName="ngModel"
                         [class.is-invalid]="customerName.invalid && customerName.touched"
                         class="form-control"
                         [(ngModel)]="customer.name"
                         name="customerName"
                         placeholder="Enter full name"
                         autocomplete="off">
                  <div *ngIf="customerName.invalid && customerName.touched" class="invalid-feedback">
                    Full name is required
                  </div>
                </div>

                <!-- ID/Passport Number -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">ID/Passport Number</label>
                  <input type="text"
                         #idNumber="ngModel"
                         class="form-control"
                         [(ngModel)]="customer.nicBr"
                         name="idNumber"
                         placeholder="Enter ID or Passport number"
                         autocomplete="off"
                         [readonly]="isEdit">
                  <small class="form-text text-muted">National ID, Passport, or other government-issued ID</small>
                </div>

                <!-- Nationality -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Nationality</label>
                  <select class="form-control"
                          [(ngModel)]="customer.nationality"
                          name="nationality">
                    <option value="">Select Nationality</option>
                    <option *ngFor="let country of countries" [value]="country">{{ country }}</option>
                  </select>
                </div>          <!-- Contact Information Section -->
                <div class="col-12 mt-4">
                  <h6 class="section-title">
                    <i class="fas fa-address-book me-2"></i>Contact Information
                  </h6>
                  <hr class="section-divider">
                </div>

                <!-- Address -->
                <div class="col-12">
                  <label class="form-label fw-semibold">Address</label>
                  <textarea class="form-control"
                            [(ngModel)]="customer.address"
                            name="address"
                            rows="2"
                            placeholder="Enter full address"
                            autocomplete="off"></textarea>
                </div>

                <!-- Email -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Email</label>
                  <input type="email"
                         #email="ngModel"
                         class="form-control"
                         [(ngModel)]="customer.email"
                         name="email"
                         [class.is-invalid]="email.invalid && email.touched"
                         placeholder="Enter email address"
                         autocomplete="off">
                  <div *ngIf="email.invalid && email.touched" class="invalid-feedback">
                    Please enter a valid email address
                  </div>
                </div>

                <!-- Country -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Country</label>
                  <select class="form-control"
                          [(ngModel)]="customer.country"
                          name="country">
                    <option value="">Select Country</option>
                    <option *ngFor="let country of countries" [value]="country">{{ country }}</option>
                  </select>
                </div>

                <!-- Primary Phone -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Primary Phone *</label>
                  <input type="tel"
                         required
                         #telephone1="ngModel"
                         [class.is-invalid]="telephone1.invalid && telephone1.touched"
                         class="form-control"
                         [(ngModel)]="customer.telephone1"
                         name="telephone1"
                         placeholder="Enter primary phone number"
                         autocomplete="off">
                  <div *ngIf="telephone1.invalid && telephone1.touched" class="invalid-feedback">
                    Primary phone number is required
                  </div>
                </div>

                <!-- Secondary Phone -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Secondary Phone</label>
                  <input type="tel"
                         class="form-control"
                         [(ngModel)]="customer.telephone2"
                         name="telephone2"
                         placeholder="Enter secondary phone number"
                         autocomplete="off">
                </div>

                <!-- Additional Information Section -->
                <div class="col-12 mt-4">
                  <h6 class="section-title">
                    <i class="fas fa-info-circle me-2"></i>Additional Information
                  </h6>
                  <hr class="section-divider">
                </div>

                <!-- Language Preference -->
                <div class="col-md-6">
                  <label class="form-label fw-semibold">Preferred Language</label>
                  <select class="form-control"
                          [(ngModel)]="customer.preferredLanguage"
                          name="preferredLanguage">
                    <option value="">Select Language</option>
                    <option value="English">English</option>
                    <option value="Sinhala">Sinhala</option>
                    <option value="Tamil">Tamil</option>
                    <option value="French">French</option>
                    <option value="German">German</option>
                    <option value="Spanish">Spanish</option>
                    <option value="Chinese">Chinese</option>
                    <option value="Japanese">Japanese</option>
                    <option value="Arabic">Arabic</option>
                  </select>
                </div>

                <!-- Active Status -->
                <div class="col-md-6">
                  <div class="form-check mt-4">
                    <input class="form-check-input"
                           type="checkbox"
                           [(ngModel)]="customer.active"
                           name="active"
                           id="activeCheck">
                    <label class="form-check-label fw-semibold" for="activeCheck">
                      Active Customer
                    </label>
                  </div>
                </div>
                <!-- Action Buttons -->
                <div class="col-12 mt-4">
                  <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="btn btn-secondary" (click)="clear()">
                      <i class="fas fa-undo me-2"></i>Clear Form
                    </button>
                    <button type="submit"
                            class="btn btn-primary"
                            [disabled]="!newPersonForm.form.valid || isProcessing">
                      <i class="fas fa-save me-2"></i>
                      {{ isProcessing ? 'Saving...' : (isEdit ? 'Update Customer' : 'Save Customer') }}
                    </button>
                  </div>
                </div>

    </div>
  </form>
</ng-template>


