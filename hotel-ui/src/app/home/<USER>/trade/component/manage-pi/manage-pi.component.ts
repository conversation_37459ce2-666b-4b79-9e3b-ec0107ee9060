import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {MetaData} from "../../../../core/model/metaData";
import {Invoice80Component} from "../invoices/invoice-80-en/invoice-80.component";
import {PurchaseInvoice} from "../../model/purchase-invoice";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";
import {Response} from "../../../../core/model/response";
import {NotificationService} from "../../../../core/service/notification.service";
import {PayBalanceComponent} from "../pay-balance/pay-balance.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {ViewSiComponent} from "../view-si/view-si.component";
import {ViewPiComponent} from "../view-pi/view-pi.component";

@Component({
standalone: false,
  selector: 'app-manage-pi',
  templateUrl: './manage-pi.component.html',
  styleUrls: ['./manage-pi.component.css']
})
export class ManagePiComponent implements OnInit {

  pis: Array<PurchaseInvoice> = [];
  selectedPi: PurchaseInvoice;

  keySupplierId: string;
  keyInvoiceNo: string;
  keyDate: Date;
  searchFilter: string;

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;

  piStatus: MetaData;
  piStatusList: Array<MetaData>;
  paymentStatusId: string;

  constructor(private piService: PurchaseInvoiceService, private modalService: BsModalService,
              private notificationService: NotificationService, private metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findAll();
    this.loadStatus();
  }

  findAll() {
    this.piService.findAllPages(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.pis = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  loadStatus(){
    this.metaDataService.findByCategory("PaymentStatus").subscribe((data: Array<MetaData>)=>{
      this.piStatusList = data;
    });
  }

  selectPi(si, index) {
    this.selectedRow = index;
    this.selectedPi = si;
  }

  searchPi() {
    if (this.searchFilter === 'inv') {
      this.piService.findAllByInvoiceNo(this.keyInvoiceNo).subscribe((data: PurchaseInvoice) => {
        this.pis = [];
        this.pis.push(data);
      });
    }
    if (this.searchFilter === 'sup') {
      this.piService.findAllBySupplier(this.keySupplierId).subscribe((data: Array<PurchaseInvoice>) => {
        this.pis = [];
        this.pis = data;
      });
    }
    if (this.searchFilter === 'date'){
      this.piService.findAllByDate(this.keyDate.toLocaleDateString()).subscribe((data: Array<PurchaseInvoice>)=>{
        this.pis = [];
        this.pis = data;
      });
    }
  }

  setSearchFilter(filter) {
    this.searchFilter = filter;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  viewDetails() {
    this.modalRef = this.modalService.show(ViewPiComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.findSi(this.selectedPi.invoiceNo);
  }

  payBalance() {
    this.modalRef = this.modalService.show(PayBalanceComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: {
        isModal: true
      }
    });
    this.modalRef.content.isPurchaseInvoice = true;
    this.modalRef.content.invoiceNo = this.selectedPi.purchaseInvoiceNo;
    this.modalRef.content.supplier = this.selectedPi.supplier;
    this.modalRef.content.totalAmount = this.selectedPi.totalAmount;
    this.modalRef.content.balance = this.selectedPi.balance;
    this.modalRef.content.bsModalRef = this.modalRef;
    // Ensure isModal is set to true
    this.modalRef.content.isModal = true;

    // Use a subscription variable to properly clean up
    const subscription = this.modalRef.onHide.subscribe(() => {
      this.findAll();
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    })
  }

  findAllByStatus() {
    this.piService.findAllByStatus(this.paymentStatusId).subscribe((data: Array<PurchaseInvoice>)=>{
      this.pis = [];
      this.pis = data;
    });
  }
}
