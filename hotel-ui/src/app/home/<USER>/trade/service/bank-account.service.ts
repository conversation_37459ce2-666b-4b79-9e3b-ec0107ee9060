import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from '../trade-constants';
import {Observable} from 'rxjs';
import {BankAccount} from '../model/bank-account';
import {BankAccountTransaction} from '../model/bank-account-transaction';
import {Response} from '../../../core/model/response';

@Injectable({
  providedIn: 'root'
})
export class BankAccountService {

  constructor(private http: HttpClient) {
  }

  // Bank Account Management
  save(bankAccount: BankAccount): Observable<Response> {
    return this.http.post<Response>(TradeConstants.SAVE_BANK_ACCOUNT, bankAccount);
  }

  findAllBankAccounts(): Observable<BankAccount[]> {
    return this.http.get<BankAccount[]>(TradeConstants.GET_ALL_BANK_ACCOUNTS);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(TradeConstants.GET_PAGES, {params:{page: page.toString(), pageSize: pageSize.toString()}});
  }

  findById(bankAccountId: string): Observable<BankAccount> {
    return this.http.get<BankAccount>(TradeConstants.SEARCH_BANK_ACCOUNT, {params: {id: bankAccountId}});
  }

  findByBankAccountNo(bankAccountNo: string): Observable<BankAccount> {
    return this.http.get<BankAccount>(TradeConstants.SEARCH_BANK_ACCOUNT_NO, {params: {any: bankAccountNo}});
  }

  findByBankName(bankName: string): Observable<BankAccount[]> {
    return this.http.get<BankAccount[]>(TradeConstants.FIND_BY_BANK_NAME, {params: {bankName: bankName}});
  }

  findByAccountHolderName(accountHolderName: string): Observable<BankAccount[]> {
    return this.http.get<BankAccount[]>(TradeConstants.FIND_BY_ACCOUNT_HOLDER_NAME, {params: {accountHolderName: accountHolderName}});
  }

  findByAccountType(accountType: string): Observable<BankAccount[]> {
    return this.http.get<BankAccount[]>(TradeConstants.FIND_BY_ACCOUNT_TYPE, {params: {accountType: accountType}});
  }

  // Bank Account Transaction Management
  createTransaction(transaction: BankAccountTransaction): Observable<Response> {
    return this.http.post<Response>(TradeConstants.CREATE_BANK_TRANSACTION, transaction);
  }

  findTransactionsByBankAccount(bankAccountId: string): Observable<BankAccountTransaction[]> {
    return this.http.get<BankAccountTransaction[]>(TradeConstants.FIND_TRANSACTIONS_BY_BANK_ACCOUNT, {params: {bankAccountId: bankAccountId}});
  }

  findTransactionsByReferenceNo(referenceNo: string): Observable<BankAccountTransaction[]> {
    return this.http.get<BankAccountTransaction[]>(TradeConstants.FIND_TRANSACTIONS_BY_REFERENCE_NO, {params: {referenceNo: referenceNo}});
  }

  findTransactionsByDateRange(startDate: string, endDate: string): Observable<BankAccountTransaction[]> {
    return this.http.get<BankAccountTransaction[]>(TradeConstants.FIND_TRANSACTIONS_BY_DATE_RANGE, {params: {startDate: startDate, endDate: endDate}});
  }

  findTransactionsByBankAccountAndDateRange(bankAccountId: string, startDate: string, endDate: string): Observable<BankAccountTransaction[]> {
    return this.http.get<BankAccountTransaction[]>(TradeConstants.FIND_TRANSACTIONS_BY_BANK_ACCOUNT_AND_DATE_RANGE,
      {params: {bankAccountId: bankAccountId, startDate: startDate, endDate: endDate}});
  }

  findTransactionsPage(bankAccountId: string, page: number, pageSize: number): Observable<any> {
    return this.http.get(TradeConstants.FIND_TRANSACTIONS_PAGE,
      {params: {bankAccountId: bankAccountId, page: page.toString(), pageSize: pageSize.toString()}});
  }

  // Balance Management
  getCurrentBalance(bankAccountId: string): Observable<number> {
    return this.http.get<number>(TradeConstants.GET_CURRENT_BALANCE, {params: {bankAccountId: bankAccountId}});
  }

  updateBalance(bankAccountId: string, amount: number, operator: string): Observable<boolean> {
    return this.http.post<boolean>(TradeConstants.UPDATE_BALANCE, null,
      {params: {bankAccountId: bankAccountId, amount: amount.toString(), operator: operator}});
  }

  debitAccount(bankAccountId: string, amount: number, referenceNo: string, referenceType: string, description: string, thirdParty?: string): Observable<boolean> {
    const params: any = {bankAccountId: bankAccountId, amount: amount.toString(), referenceNo: referenceNo, referenceType: referenceType, description: description};
    if (thirdParty) {
      params.thirdParty = thirdParty;
    }
    return this.http.post<boolean>(TradeConstants.DEBIT_ACCOUNT, null, {params: params});
  }

  creditAccount(bankAccountId: string, amount: number, referenceNo: string, referenceType: string, description: string, thirdParty?: string): Observable<boolean> {
    const params: any = {bankAccountId: bankAccountId, amount: amount.toString(), referenceNo: referenceNo, referenceType: referenceType, description: description};
    if (thirdParty) {
      params.thirdParty = thirdParty;
    }
    return this.http.post<boolean>(TradeConstants.CREDIT_ACCOUNT, null, {params: params});
  }

  processPurchasePayment(bankAccountId: string, amount: number, purchaseInvoiceNo: string, supplierName: string): Observable<boolean> {
    return this.http.post<boolean>(TradeConstants.PROCESS_PURCHASE_PAYMENT, null,
      {params: {bankAccountId: bankAccountId, amount: amount.toString(), purchaseInvoiceNo: purchaseInvoiceNo, supplierName: supplierName}});
  }

  // Utility Methods
  getLastTransaction(bankAccountId: string): Observable<BankAccountTransaction> {
    return this.http.get<BankAccountTransaction>(TradeConstants.GET_LAST_TRANSACTION, {params: {bankAccountId: bankAccountId}});
  }

  calculateBalanceFromTransactions(bankAccountId: string): Observable<number> {
    return this.http.get<number>(TradeConstants.CALCULATE_BALANCE_FROM_TRANSACTIONS, {params: {bankAccountId: bankAccountId}});
  }

  validateSufficientBalance(bankAccountId: string, amount: number): Observable<boolean> {
    return this.http.get<boolean>(TradeConstants.VALIDATE_SUFFICIENT_BALANCE, {params: {bankAccountId: bankAccountId, amount: amount.toString()}});
  }
}
