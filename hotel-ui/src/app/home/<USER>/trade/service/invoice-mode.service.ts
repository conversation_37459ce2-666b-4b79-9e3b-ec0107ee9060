import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { UserSettingsService } from '../../../admin/service/user-settings.service';
import { SettingsService } from '../../../core/service/settings.service';

@Injectable({
  providedIn: 'root'
})
export class InvoiceModeService {
  private readonly INVOICE_MODE_KEY = 'invoiceCreationMode';

  constructor(
    private router: Router,
    private userSettingsService: UserSettingsService,
    private settingsService: SettingsService
  ) { }

  /**
   * Navigate to the appropriate invoice creation component based on user settings
   */
  navigateToInvoiceCreation(): void {
    this.getUserPreferredMode().then(mode => {
      try {
        console.log('Navigating to invoice creation with mode:', mode);
        if (mode === 'sales_rep') {
          // Navigate to the sales rep invoice component
          this.router.navigate(['/sales_rep_invoice'])
            .then(success => {
              if (!success) {
                console.error('Navigation to sales_rep_invoice failed');
                // Fallback to standard invoice if navigation fails
                this.router.navigate(['/new_sales_invoice']);
              }
            })
            .catch(err => {
              console.error('Error navigating to sales_rep_invoice:', err);
              // Fallback to standard invoice if navigation throws an error
              this.router.navigate(['/new_sales_invoice']);
            });
        } else if (mode === 'distributor') {
          // Navigate to the distributor invoice component
          this.router.navigate(['/distributor_invoice'])
            .then(success => {
              if (!success) {
                console.error('Navigation to distributor_invoice failed');
                // Fallback to standard invoice if navigation fails
                this.router.navigate(['/new_sales_invoice']);
              }
            })
            .catch(err => {
              console.error('Error navigating to distributor_invoice:', err);
              // Fallback to standard invoice if navigation throws an error
              this.router.navigate(['/new_sales_invoice']);
            });
        } else {
          // Default to standard invoice creation
          this.router.navigate(['/new_sales_invoice'])
            .then(success => {
              if (!success) {
                console.error('Navigation to new_sales_invoice failed');
                // If navigation fails, try direct component route
                this.router.navigate(['/new_sales_invoice']);
              }
            })
            .catch(err => {
              console.error('Error navigating to new_sales_invoice:', err);
              // If navigation throws an error, try direct component route
              this.router.navigate(['/new_sales_invoice']);
            });
        }
      } catch (e) {
        console.error('Exception in navigateToInvoiceCreation:', e);
        // Fallback to direct component route if an exception occurs
        this.router.navigate(['/new_sales_invoice']);
      }
    }).catch(err => {
      console.error('Error getting preferred mode:', err);
      // Fallback to standard invoice if getting the preferred mode fails
      this.router.navigate(['/new_sales_invoice']);
    });
  }

  /**
   * Get the user's preferred invoice creation mode
   * @returns Promise with the preferred mode
   */
  getUserPreferredMode(): Promise<string> {
    return new Promise<string>((resolve) => {
      // First check settings service for faster access
      try {
        const mode = this.settingsService.getSetting(this.INVOICE_MODE_KEY, null);
        if (mode) {
          console.log('Using invoice mode from SettingsService:', mode);
          resolve(mode);
          return;
        }
      } catch (e) {
        console.error('Error getting settings from SettingsService', e);
      }

      // If not found in localStorage, check user settings from server
      this.userSettingsService.getSettingValue(this.INVOICE_MODE_KEY).subscribe(
        value => {
          if (value) {
            console.log('Using invoice mode from user settings:', value);
            resolve(value);
          } else {
            const defaultMode = this.settingsService.getSetting('invoiceCreationMode', 'standard');
            console.log('No invoice mode setting found, using default mode:', defaultMode);
            resolve(defaultMode);
          }
        },
        error => {
          console.error('Error getting invoice mode setting:', error);
          const defaultMode = this.settingsService.getSetting('invoiceCreationMode', 'standard');
          console.log('Using default mode due to error:', defaultMode);
          resolve(defaultMode);
        }
      );
    });
  }

  /**
   * Save the user's preferred invoice creation mode
   * @param mode The preferred mode ('standard', 'sales_rep', or 'distributor')
   * @returns Promise that resolves to true if successful, false otherwise
   */
  savePreferredMode(mode: string): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      // Save to user settings service
      this.userSettingsService.updateSetting(this.INVOICE_MODE_KEY, mode, true).subscribe(
        success => {
          if (success) {
            console.log('Saved invoice mode preference to user settings:', mode);
            resolve(true);
          } else {
            console.error('Failed to save invoice mode preference to user settings');
            resolve(false);
          }
        },
        error => {
          console.error('Error saving invoice mode preference to user settings:', error);
          resolve(false);
        }
      );
    });
  }
}
