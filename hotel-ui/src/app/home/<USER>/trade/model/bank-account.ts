export class BankAccount{

  public id: string;

  public bankName: string;

  public accountNo: string;

  public branch: string;

  public accountHolderName: string;

  public accountType: string; // SAVINGS, CURRENT, FIXED_DEPOSIT

  public currentBalance: number;

  public openingBalance: number;

  public remark: string;

  public active: boolean;

  public createdBy: string;

  public createdDate: Date;

  public lastModifiedBy: string;

  public lastModifiedDate: Date;
}
