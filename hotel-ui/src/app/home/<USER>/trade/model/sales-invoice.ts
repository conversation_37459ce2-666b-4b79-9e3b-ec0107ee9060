import {SalesInvoiceRecord} from './sales-invoice-record';
import {MetaData} from '../../../core/model/metaData';
import {Cheque} from "./cheque";

export class SalesInvoice {

  id: string;

  invoiceNo: string;

  reference: string;

  date: string;

  advancePayment: number;

  subTotal: number;

  totalAmount: number;

  balance: number;

  totalDiscount: number;

  serviceCharge: number;

  payment: number;

  cashBalance: number;

  warehouseCode: number;

  paymentMethod: MetaData;

  cheque: Cheque;

  cashlessAmount: number;

  cashAmount: number;

  cardOrVoucherNo: string;

  chequeNo: string;

  dueDate: Date;

  status: MetaData;

  salesType: MetaData;

  salesInvoiceRecords: Array<SalesInvoiceRecord>;

  customerName: string;

  customerNo: string;

  paymentDate: string;

  createdBy: string;

  routeNo: string;

  routeName: string;

  cashierUserName: string;

  drawerNo: string;

  tableNo: string;

  // Invoice type identification
  invoiceType: string; // "ORDER", "TRADE", "BOOKING", "BAR", "RESTAURANT"

  // Order number for invoices created from orders
  orderNo: string;

  // Legacy flag - kept for backward compatibility
  convertedFromOrders: boolean;

}
