package lk.sout.loanManager.business.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.business.entity.Payment;

import java.util.List;

public interface LoanRecordService {

    boolean save(LoanRecord loanRecord);

    List<LoanRecord> findAll();

    LoanRecord findById(String id);

    List<LoanRecord> findByStatus(MetaData metaData);

    List<LoanRecord> findByStatusAndLoanNo(MetaData metaData, String loanNo);

    List<LoanRecord> findByStatusAndLoanNoOrderByBalance(MetaData metaData, String loanNo);

    int countByLoanNo(String loanNo);

    int countByStatusAndLoanNo(MetaData metaData, String loanNo);

    List<LoanRecord> findAllRecordsByLoanNo(String loanNo);

    List<LoanRecord> findAllRecordsByLoanNoAndStatusNot(String loanNo, MetaData status);

    List<LoanRecord> findPendingRecordsByNic(String nic);

    List<LoanRecord> findPendingRecordsByTp1(String tp1);

    List<LoanRecord> findPendingRecordsByLoanNo(String loan);

    List<LoanRecord> findTodayList();

    List<LoanRecord> findTodayLoanByName(String name);

    List<LoanRecord> findArrearsList();

    List<LoanRecord> findArrearsLoanByName(String name);

    List<LoanRecord> findUnPaidTodayList();

    Response create(String loanNo, String mode, double installment);

    Response pay(String loanRecId, double payment, String appNo);

    Response amend(String loanRecId, String paymentId, String appNo);

    Response createAndPayLoanRecord(String loanNo, double payment, String appNo);
}
