package lk.sout.config;

import lk.sout.core.entity.*;
import lk.sout.core.entity.Module;
import lk.sout.core.service.PermissionService;
import lk.sout.loanManager.borrower.entity.Borrower;
import lk.sout.loanManager.business.entity.Ledger;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.business.entity.Route;
import lk.sout.loanManager.business.repository.LedgerRepository;
import lk.sout.loanManager.business.repository.LoanPlanRepository;
import lk.sout.loanManager.borrower.repository.*;
import lk.sout.core.repository.*;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.repository.LoanRecordRepository;
import lk.sout.loanManager.business.repository.LoanRepository;
import lk.sout.loanManager.business.service.LedgerService;
import lk.sout.loanManager.business.service.LoanRecordService;
import lk.sout.loanManager.business.service.LoanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
public class InitDataRunner implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    BCryptPasswordEncoder bCryptPasswordEncoder;

    @Autowired
    PermissionService permissionService;

    @Autowired
    CompanyRepository companyRepository;

    @Autowired
    UserGroupRepository userGroupRepository;

    @Autowired
    SequenceRepository seqRepository;

    @Autowired
    ModuleRepository moduleRepository;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ActionRepository actionRepository;

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    BorrowerRepository borrowerRepository;

    @Autowired
    LoanPlanRepository loanPlanRepository;

    @Autowired
    LoanRecordRepository loanRecordRepository;

    @Autowired
    LoanRecordService loanRecordService;

    @Autowired
    LoanService loanService;

    @Autowired
    LoanRecord loanRecord;

    @Autowired
    LedgerRepository ledgerRepository;

    @Override
    public void run(String... strings) throws Exception {
        try {
            if (userRoleRepository.count() < 1) {
                List<UserRole> userRoles = new ArrayList<>();
                for (RoleName autho : RoleName.values()) {
                    UserRole userRole = new UserRole();
                    userRole.setName(autho);
                    userRoles.add(userRole);
                }
                userRoleRepository.saveAll(userRoles);
            }

            if (companyRepository.count() < 1) {
                Company thisBusiness = new Company();
                thisBusiness.setLogo("");
                thisBusiness.setName("");
                thisBusiness.setSlogan("");
                companyRepository.save(thisBusiness);
            }

            if (moduleRepository.count() < 1) {
                Module admin = new Module("Admin", "Admin", true);
                moduleRepository.save(admin);
                Module hr = new Module("HR", "HR", true);
                moduleRepository.save(hr);
                Module business = new Module("Business", "Business", true);
                moduleRepository.save(business);
                Module borrower = new Module("Borrower", "Borrower", true);
                moduleRepository.save(borrower);
                Module report = new Module("Report", "Report", true);
                moduleRepository.save(report);
            }

            Module adminMod = moduleRepository.findByName("Admin");
            Permission newUser = new Permission("Create User", "admin/new_user", true, "fas fa-user-plus", adminMod);
            Permission manageUser = new Permission("User Management", "admin/manage_users", true, "fas fa-users-cog", adminMod);
            Permission companyDetails = new Permission("Setup Company", "admin/company_detail", true, "fas fa-building", adminMod);
            Permission createLoanPlan = new Permission("Create Loan Plan", "business/create_loan_plan", true, "fas fa-clipboard-list", adminMod);
            Permission manageLoanPlan = new Permission("Manage Loan Plan", "business/manage_loan_plan", true, "fas fa-tasks", adminMod);
            Permission manageRoutes = new Permission("Manage Routes", "business/manage_routes", true, "fas fa-route", adminMod);
            Permission dashboard = new Permission("Dashboard", "dashboard", true, "fas fa-tachometer-alt", adminMod);

            permissionService.saveIfUnavailable(newUser);
            permissionService.saveIfUnavailable(manageUser);
            permissionService.saveIfUnavailable(companyDetails);
            permissionService.saveIfUnavailable(createLoanPlan);
            permissionService.saveIfUnavailable(manageLoanPlan);
            permissionService.saveIfUnavailable(manageRoutes);
            permissionService.saveIfUnavailable(dashboard);

            Module hrMod = moduleRepository.findByName("HR");
            Permission newCollectorRoute = new Permission("New Collector", "hr/new_collector", true, "fas fa-user-tie", hrMod);
            Permission manageCollectorRoute = new Permission("Manage Collector", "hr/manage_collector", true, "fas fa-users", hrMod);

            permissionService.saveIfUnavailable(newCollectorRoute);
            permissionService.saveIfUnavailable(manageCollectorRoute);

            Module businessMod = moduleRepository.findByName("Business");
            Permission createLoan = new Permission("Create Loan", "business/create_loan", true, "fas fa-plus-circle", businessMod);
            Permission pendingLoans = new Permission("Pending Loans", "business/pending_loan", true, "fas fa-clock", businessMod);
            Permission arrearsLoans = new Permission("Arrears Loans", "business/arrears_loan", true, "fas fa-exclamation-triangle", businessMod);
            Permission allLoans = new Permission("All Loan", "business/all_loan", true, "fas fa-list-alt", businessMod);
            Permission loanPayment = new Permission("Loan Payment", "business/loan_payment", true, "fas fa-money-bill-wave", businessMod);
            Permission myLoan = new Permission("My Loan", "business/my_loan", true, "fas fa-user-check", businessMod);
            Permission ledger = new Permission("Ledger", "business/ledger", true, "fas fa-book", businessMod);
            Permission holidayManagement = new Permission("Holiday Management", "business/holiday_management", true, "fas fa-calendar-times", businessMod);

            permissionService.saveIfUnavailable(createLoan);
            permissionService.saveIfUnavailable(pendingLoans);
            permissionService.saveIfUnavailable(arrearsLoans);
            permissionService.saveIfUnavailable(allLoans);
            permissionService.saveIfUnavailable(loanPayment);
            permissionService.saveIfUnavailable(myLoan);
            permissionService.saveIfUnavailable(ledger);
            permissionService.saveIfUnavailable(holidayManagement);

            Module customerMod = moduleRepository.findByName("Borrower");
            Permission newBorrower = new Permission("Create Borrower", "borrower/create_borrower", true, "fas fa-user-plus", customerMod);
            Permission manageBorrower = new Permission("Manage Borrower", "borrower/manage_borrower", true, "fas fa-address-book", customerMod);

            permissionService.saveIfUnavailable(newBorrower);
            permissionService.saveIfUnavailable(manageBorrower);

            Module reportMod = moduleRepository.findByName("Report");
            Permission customerReport = new Permission("Borrower Report", "report/borrower", true, "fas fa-chart-bar", reportMod);
            Permission paymentReport = new Permission("Payment Report", "report/payment", true, "fas fa-chart-line", reportMod);
            Permission loanReport = new Permission("Loan Report", "report/loan", true, "fas fa-chart-pie", reportMod);
            Permission incomeReport = new Permission("Income Report", "report/income", true, "fas fa-chart-area", reportMod);

            permissionService.saveIfUnavailable(customerReport);
            permissionService.saveIfUnavailable(paymentReport);
            permissionService.saveIfUnavailable(loanReport);
            permissionService.saveIfUnavailable(incomeReport);

            if (userRepository.count() < 1) {
                User user = new User();
                user.setUserRoles(userRoleRepository.findAll());
                user.setEmail("<EMAIL>");
                user.setActive(true);
                user.setFirstName("admin");
                user.setLastName("admin");
                user.setUsername("admin");
                user.setPermissions(permissionService.findAll());
                user.setPassword(bCryptPasswordEncoder.encode("admin"));
                userRepository.save(user);
            }

            if (seqRepository.count() < 1) {
                Sequence sequence = new Sequence();
                sequence.setCounter(1000);
                sequence.setName("Loan");
                sequence.setPrefix("L");
                seqRepository.save(sequence);
            }

            // saving meta data
            MetaData gender1 = new MetaData("Gender", "Male", "Male");
            metaDataService.saveIfUnavailable(gender1);

            MetaData gender2 = new MetaData("Gender", "Female", "Female");
            metaDataService.saveIfUnavailable(gender2);

            MetaData transactionType1 = new MetaData("TransactionType", "type1", "income");
            metaDataService.saveIfUnavailable(transactionType1);

            MetaData transactionType2 = new MetaData("TransactionType", "type2", "expense");
            metaDataService.saveIfUnavailable(transactionType2);

            MetaData loanStatusPending = new MetaData("Loan Status", "1", "Pending");
            metaDataService.saveIfUnavailable(loanStatusPending);

            MetaData loanStatusIssued = new MetaData("Loan Status", "2", "Issued");
            metaDataService.saveIfUnavailable(loanStatusIssued);

            MetaData loanStatusCurrent = new MetaData("Loan Status", "3", "Current");
            metaDataService.saveIfUnavailable(loanStatusCurrent);

            MetaData loanStatusArrears = new MetaData("Loan Status", "4", "Arrears");
            metaDataService.saveIfUnavailable(loanStatusArrears);

            MetaData loanStatusCompleted = new MetaData("Loan Status", "5", "Settled");
            metaDataService.saveIfUnavailable(loanStatusCompleted);

            MetaData loanRecordPending = new MetaData("Loan Record Status", "1", "Payment Pending");
            metaDataService.saveIfUnavailable(loanRecordPending);

            MetaData loanRecordArrears = new MetaData("Loan Record Status", "2", "Arrears");
            metaDataService.saveIfUnavailable(loanRecordArrears);

            MetaData loanRecordSettled = new MetaData("Loan Record Status", "3", "Settled");
            metaDataService.saveIfUnavailable(loanRecordSettled);

            MetaData paymentMethod1 = new MetaData("PaymentMethod", "PaymentMethod_Cash", "Cash");
            metaDataService.saveIfUnavailable(paymentMethod1);

            MetaData paymentMethod2 = new MetaData("PaymentMethod", "PaymentMethod_Cheque", "Cheque");
            metaDataService.saveIfUnavailable(paymentMethod2);

            MetaData paymentMethod3 = new MetaData("PaymentMethod", "PaymentMethod_Credit", "Credit");
            metaDataService.saveIfUnavailable(paymentMethod3);

            MetaData paymentStatusType1 = new MetaData("PaymentStatus", "PaymentStatus_Pending", "Pending");
            metaDataService.saveIfUnavailable(paymentStatusType1);

            MetaData paymentStatusType2 = new MetaData("PaymentStatus", "PaymentStatus_Partially_Paid", "Partially Paid");
            metaDataService.saveIfUnavailable(paymentStatusType2);

            MetaData paymentStatusType3 = new MetaData("PaymentStatus", "PaymentStatus_Paid", "Paid");
            metaDataService.saveIfUnavailable(paymentStatusType3);

            MetaData paymentStatusType4 = new MetaData("PaymentStatus", "PaymentStatus_Daily Paid", "Daily Paid");
            metaDataService.saveIfUnavailable(paymentStatusType4);

            MetaData paymentStatusType5 = new MetaData("PaymentStatus", "PaymentStatus_Arrears", "Arrears");
            metaDataService.saveIfUnavailable(paymentStatusType5);


            if (actionRepository.count() < 1) {
                Action action = new Action();
                Action ac = actionRepository.save(action);
                actionRepository.delete(ac);
            }

            if (transactionRepository.count() < 1) {
                Transaction transaction = transactionRepository.save(new Transaction());
                transactionRepository.delete(transaction);
            }

            if (ledgerRepository.findAll().size() == 0) {
                Ledger ledgerObj = new Ledger();
                ledgerObj.setAppNo("01");
                ledgerObj.setCurrentBalance(0.0);
                ledgerRepository.save(ledgerObj);
            }

/*            MetaData settledStatus = metaDataService.searchMetaData("Settled", "Loan Status");
          //  List<Loan> loans = loanService.findAllByStatusNot(settledStatus);

            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Status");
            MetaData current = metaDataService.searchMetaData("Current", "Loan Status");

            MetaData arrearsLoanRec = metaDataService.searchMetaData("Arrears", "Loan Record Status");

       //     for (Loan loan : loans) {
                Loan loan = loanService.findByLoanNo("L1238");
                if (loan.getStatus().getId().equals(arrears.getId())) {
                    loan.setStatus(current);
                }
                List<LoanRecord> loanRecords = loanRecordService.findAllRecordsByLoanNo(loan.getLoanNo());
                double totalPaid = 0.0;
                double overPaidAmount = 0.0;
                for (LoanRecord loanRecord : loanRecords) {
                    totalPaid = totalPaid + loanRecord.getPaidAmount();
                    if (loanRecord.getPaidAmount() > 0) {
                        overPaidAmount = overPaidAmount + (loanRecord.getPaidAmount() - loanRecord.getInstallmentAmount());
                    }
                    if (loanRecord.getPaidAmountByOverPaid() > 0) {
                        loanRecord.setStatus(arrearsLoanRec);
                    }
                    loanRecord.setPaidAmountByOverPaid(0);
                    loanRecord.setInstallmentAmount(loan.getInstallmentAmount());
                    loanRecord.setBalance(loan.getInstallmentAmount() - loanRecord.getPaidAmount());
                     loanRecordService.save(loanRecord);
                }

                loan.setOverPaidAmount(overPaidAmount);
                loan.setLoanAmountWithInterest(loan.getLoanSettlementAmount());
                loan.setArrearsAmount(0);
                loan.setBalance(loan.getLoanSettlementAmount() - loan.getPaidAmount());
                loanService.save(loan);
                System.out.println(loan.getLoanNo() + " " + totalPaid + " " + loan.getPaidAmount() + " " + overPaidAmount + " " + loan.getOverPaidAmount());
         //   }
            //System.out.println("Done");
            //-------------
            //Loan loan = loanService.findByLoanNo("L1220");
            double overPayment = loan.getOverPaidAmount();
            if (overPayment > 0) {
                MetaData arrears2 = metaDataService.searchMetaData("Arrears", "Loan Record Status");
                MetaData settled = metaDataService.searchMetaData("Settled", "Loan Record Status");
                List<LoanRecord> loanRecords2 = loanRecordService.findByStatusAndLoanNoOrderByBalance(arrears2, loan.getLoanNo());
                for (LoanRecord loanRecord : loanRecords2) {
                    double recBalance = loan.getInstallmentAmount() - loanRecord.getPaidAmount();

                    if (overPayment >= recBalance) {
                        loanRecord.setPaidAmountByOverPaid(recBalance);
                        loanRecord.setPaidDate(LocalDate.now());
                        loanRecord.setStatusCode(settled.getName());
                        loanRecord.setStatus(settled);
                        overPayment = overPayment - recBalance;
                        loanRecordService.save(loanRecord);
                        System.out.println("Over payment updated for - " + loan.getLoanNo());
                    }
                }
                loan.setOverPaidAmount(overPayment);
                loanService.save(loan);
                System.out.println("Over payment done for - " + loan.getLoanNo());
            }*/

        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

}
