spring.application.name=General_Hotel

# Graceful shutdown
server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=30s

# WhatsApp Integration Configuration
# Enable/disable WhatsApp daily summary reports
whatsapp.enabled=true

# WhatsApp Business API Configuration
# Get these from your WhatsApp Business API provider (e.g., Meta, Twilio, etc.)
whatsapp.api.url=https://graph.facebook.com/v18.0/***********
whatsapp.api.token=CmUKIQjfzKGfx4W5AxIGZW50OndhIghWaWdhbmFuYVDy6pHEBhpA71bmRk4hVdD8CqdWCXKwwGOGc07QbCsyD6dsrG/Zuxzc6WKq+5FPjNq7sfOGHPdfnRquBxaOJmZ63R9jVRCwBRIubVgZ2OW6sqTzWrWznaloIZRZ5eBUxtjvrF1FTq08ii12brt2xDGOXkaDPAwHzw==

# Owner's WhatsApp number (with country code, e.g., +***********)
whatsapp.owner.phone=+***********

# MongoDB Configuration
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=**************
spring.data.mongodb.port=27017
spring.data.mongodb.database=generalHotel
spring.data.mongodb.authDatabase=admin
spring.data.mongodb.username=generalHotel
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# Disable MongoDB transactions temporarily to fix session pool issues
spring.data.mongodb.auto-index-creation=true

# Multi-Tenancy Configuration
# Set to true to enable multi-tenancy (subdomain-based database routing)
# Set to false to use single database for all requests
multitenancy.enabled=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=always
management.endpoints.web.base-path=/actuator
management.endpoint.health.enabled=true
management.endpoint.info.enabled=true
management.endpoint.metrics.enabled=true
management.security.enabled=true

# Health Check Configuration
management.health.mongo.enabled=true
management.health.diskspace.enabled=true
management.health.ping.enabled=true

# Info Endpoint Configuration
info.app.name=Viganana Service
info.app.description=ERP Hotel Service
info.app.version=1.0
info.app.encoding=@project.build.sourceEncoding@
info.app.java.version=@java.version@

# MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB

spring.main.allow-circular-references=true

sout.mainWhCode=0

# Backup Configuration
backup.enabled=true
backup.retention.days=30
backup.temp.directory=/opt/tomcat11/backup/temp-backups
# backup.databases - Will auto-discover all databases starting with "generalHotel"
backup.database.pattern=generalHotel*

# Google Drive Configuration
google.drive.credentials.path=/opt/tomcat11/backup/sout-main-439195d6196b.json
google.drive.folder.id=1QX-0qNbc9USEMc6zs-fppG5OIR31AjGo

# Logging Configuration
logging.level.root=WARN
logging.level.lk.sout=INFO

logging.file.name=/opt/hotel-service/logs/hotel-service.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Date Formats
dateFormat=MM/dd/yyyy
dateTimeFormat=MM/dd/yyyy HH:mm:ss
