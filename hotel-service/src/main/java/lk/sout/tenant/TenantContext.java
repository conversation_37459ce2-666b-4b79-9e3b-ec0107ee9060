package lk.sout.tenant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

/**
 * ThreadLocal holder for current tenant context
 *
 * Stores the current tenant identifier extracted from the request
 * and provides methods to access it during database resolution.
 */
@Component
public class TenantContext {

    private static final Logger logger = LoggerFactory.getLogger(TenantContext.class);

    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();

    @Value("${spring.data.mongodb.database}")
    private String defaultDatabaseInstance;

    @Value("${multitenancy.enabled:true}")
    private boolean multiTenancyEnabledInstance;

    private static String defaultDatabase;

    private static boolean multiTenancyEnabled;

    /**
     * PostConstruct method to initialize static fields from injected values
     */
    @PostConstruct
    private void init() {
        TenantContext.defaultDatabase = this.defaultDatabaseInstance;
        TenantContext.multiTenancyEnabled = this.multiTenancyEnabledInstance;
        logger.info("🔧 TenantContext initialized - Default DB: '{}', Multi-tenancy: {} ({})",
                   defaultDatabase, multiTenancyEnabled ? "ENABLED" : "DISABLED", multiTenancyEnabled);
    }
    
    /**
     * Set the current tenant for this thread
     * 
     * @param tenant tenant identifier (subdomain)
     */
    public static void setCurrentTenant(String tenant) {
        currentTenant.set(tenant);
        logger.trace("🔧 Set current tenant: {}", tenant);
    }
    
    /**
     * Get the current tenant for this thread
     * 
     * @return tenant identifier or "default" if not set
     */
    public static String getCurrentTenant() {
        String tenant = currentTenant.get();
        if (tenant == null || tenant.trim().isEmpty()) {
            logger.debug("⚠️ No tenant in context, returning default");
            return "default";
        }
        return tenant;
    }
    
    /**
     * Clear the current tenant context
     * Should be called in finally block to prevent memory leaks
     */
    public static void clear() {
        String tenant = currentTenant.get();
        currentTenant.remove();
        logger.trace("🧹 Cleared tenant context: {}", tenant);
    }

    /**
     * Check if multi-tenancy is enabled
     *
     * @return true if multi-tenancy is enabled, false otherwise
     */
    public static boolean isMultiTenancyEnabled() {
        return multiTenancyEnabled;
    }
    
    /**
     * Get the database name for the current tenant
     *
     * If multi-tenancy is disabled, always returns the default database.
     * If multi-tenancy is enabled, maps tenant identifier to actual database name:
     * - "default" -> configured default database (from application.properties)
     * - "demo" -> defaultDatabase + "Demo" (e.g., "generalHotelDemo")
     * - "client1" -> defaultDatabase + "Client1" (e.g., "generalHotelClient1")
     * - etc.
     *
     * @return database name for current tenant
     */
    public static String getCurrentDatabase() {
        // If multi-tenancy is disabled, always use default database
        if (!multiTenancyEnabled) {
            return defaultDatabase;
        }

        logger.debug("🔓 Multi-tenancy ENABLED ({}), processing tenant logic", multiTenancyEnabled);

        String tenant = getCurrentTenant();

        if ("default".equals(tenant)) {
            return defaultDatabase;
        }

        // Get base database name (remove any existing suffix if present)
        String baseDatabaseName = getBaseDatabaseName();

        // Capitalize first letter for database name
        String capitalizedTenant = tenant.substring(0, 1).toUpperCase() + tenant.substring(1).toLowerCase();
        String databaseName = baseDatabaseName + capitalizedTenant;

        logger.trace("🗄️ Mapped tenant '{}' to database '{}'", tenant, databaseName);
        return databaseName;
    }

    /**
     * Extract base database name from the configured default database
     * This handles cases where the default database might already have a suffix
     * 
     * @return base database name for tenant mapping
     */
    private static String getBaseDatabaseName() {
        if (defaultDatabase == null || defaultDatabase.trim().isEmpty()) {
            logger.warn("⚠️ Default database is null or empty, using 'generalHotel' as fallback");
            return "generalHotel";
        }

        // If the default database is exactly "generalHotel", use it as is
        if ("generalHotel".equals(defaultDatabase)) {
            return defaultDatabase;
        }

        // If it starts with "generalHotel" but has additional suffix, extract the base
        if (defaultDatabase.startsWith("generalHotel")) {
            return "generalHotel";
        }

        // For any other database name, use it as the base
        logger.debug("🔍 Using custom database base: '{}'", defaultDatabase);
        return defaultDatabase;
    }
}
