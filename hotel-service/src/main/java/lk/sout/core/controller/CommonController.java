package lk.sout.core.controller;

import lk.sout.core.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/3/2019
 */
@RestController
@RequestMapping("/common")
public class CommonController {

    @Autowired
    CommonService commonService;

    @RequestMapping(value = "/findRelatedRoutes", method = RequestMethod.GET)
    private ResponseEntity<?> findRelatedRoutes(@RequestParam("route") String route) {
        try {
            return ResponseEntity.ok(commonService.findRelatedRoutes(route));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}

