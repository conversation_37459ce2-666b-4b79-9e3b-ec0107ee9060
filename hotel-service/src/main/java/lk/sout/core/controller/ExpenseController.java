package lk.sout.core.controller;

import lk.sout.core.entity.Expense;
import lk.sout.core.service.ExpenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/expense")
public class ExpenseController {

    @Autowired
    ExpenseService expenseService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Expense expense) {
        try {
            return ResponseEntity.ok(expenseService.save(expense));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPage", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPage(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByTypeCategory", method = RequestMethod.GET)
    private ResponseEntity<?> findByTypeCategory(@RequestParam("catId") String catId, @RequestParam("page") String page,
                                                   @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByExpenseCategory(catId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByExpenseType", method = RequestMethod.GET)
    private ResponseEntity<?> findByExpenseType(@RequestParam("typeId") String expense, @RequestParam("page") String page,
                                                   @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByExpenseType(expense, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByEmployee", method = RequestMethod.GET)
    private ResponseEntity<?> find_by_employee(@RequestParam("empId") String employee, @RequestParam("page") String page,
                                               @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByEmployee(employee, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBetweenDates", method = RequestMethod.GET)
    private ResponseEntity<?> findBetweenDates(@RequestParam String fromDate, @RequestParam String toDate,
                                               @RequestParam("page") String page,
                                               @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByDateBetween(LocalDate.parse(fromDate), LocalDate.parse(toDate),
                    PageRequest.of(Integer.parseInt(page),
                            Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDateRangeFilter", method = RequestMethod.GET)
    private ResponseEntity<?> findByDateRangeFilter(@RequestParam("rangeId") String rangeId, @RequestParam("page") String page,
                                                    @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByDateRangeFilter(rangeId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByTypeAndBetweenDates", method = RequestMethod.GET)
    private ResponseEntity<?> findByTypeAndBetweenDates(@RequestParam String typeId, @RequestParam String fromDate,
                                                        @RequestParam String toDate) {
        try {
            return ResponseEntity.ok(expenseService.findByTypeAndDateBetween(typeId, LocalDate.parse(fromDate),
                    LocalDate.parse(toDate)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByEmployeeAndBetweenDates", method = RequestMethod.GET)
    private ResponseEntity<?> findByEmployeeAndBetweenDates(@RequestParam String empId, @RequestParam String fromDate,
                                                            @RequestParam String toDate) {
        try {
            return ResponseEntity.ok(expenseService.findByEmployeeAndDateBetween(empId, LocalDate.parse(fromDate),
                    LocalDate.parse(toDate)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByTypeAndEmployeeAndBetweenDates", method = RequestMethod.GET)
    private ResponseEntity<?> findByTypeAndEmployeeAndBetweenDates(@RequestParam String typeId, @RequestParam String empId,
                                                                   @RequestParam String fromDate, @RequestParam String toDate) {
        try {
            return ResponseEntity.ok(expenseService.findByTypeAndEmployeeAndDateBetween(typeId, empId, LocalDate.parse(fromDate),
                    LocalDate.parse(toDate)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByTypeAndEmployee", method = RequestMethod.GET)
    private ResponseEntity<?> findByTypeAndEmployee(@RequestParam String typeId, @RequestParam String empId,
                                                    @RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseService.findByEmployeeAndType(empId, typeId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}

