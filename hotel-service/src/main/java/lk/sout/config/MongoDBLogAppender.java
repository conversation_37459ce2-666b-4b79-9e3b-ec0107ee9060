package lk.sout.config;

import ch.qos.logback.core.AppenderBase;
import ch.qos.logback.classic.spi.ILoggingEvent;
import lk.sout.core.entity.LogEntry;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.time.LocalDateTime;

public class MongoDBLogAppender extends AppenderBase<ILoggingEvent> {

    private final MongoTemplate mongoTemplate;

    // Prevent infinite logging loops by tracking if we're currently saving a log
    private static final ThreadLocal<Boolean> isLogging = new ThreadLocal<>();

    public MongoDBLogAppender(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    protected void append(ILoggingEvent event) {
        // Prevent infinite logging loops
        if (Boolean.TRUE.equals(isLogging.get())) {
            return;
        }



        isLogging.set(true);
        try {
            LogEntry logEntry = new LogEntry();
            logEntry.setTimestamp(LocalDateTime.now());
            logEntry.setLevel(event.getLevel().toString());
            logEntry.setLogger(event.getLoggerName());
            logEntry.setMessage(event.getFormattedMessage());
            logEntry.setThreadName(event.getThreadName());

            mongoTemplate.save(logEntry);
        } catch (Exception e) {
            // Don't log this error to prevent infinite loops
            System.err.println("Failed to save log entry: " + e.getMessage());
        } finally {
            isLogging.remove();
        }
    }
}
