package lk.sout.business.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.business.inventory.entity.Brand;
import lk.sout.business.inventory.entity.Item;
import lk.sout.business.inventory.entity.ItemCategory;
import lk.sout.business.inventory.entity.SubCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemService {

    Response save(Item item);

    boolean remove(String id);

    Iterable<Item> findAll(Pageable pageable);

    Item findOne(String id);

    List<Item> findAllByItemCodeLike(String code);

    List<Item> findAllByBarcodeLike(String code);

    List<Item> findByCategory(ItemCategory category);

    List<Item> findBySubCategory(SubCategory subCategory);

    List<Item> findByBrand(Brand brand);

    List<Item> findActiveByNameLike(String name);

    List<Item> findActiveServiceByNameLike(String name);

    List<Item> findActiveServiceByCodeLike(String name);

    List<Item> findIngredientsByName(String itemName);

    List<Item> findFoodsByName(String itemName);

    List<Item> findEventItemsByName(String itemName);

    List<Item> findAllByNameLike(String name);

    List<Item> findByAny(String name);

    Item findOneByItemCode(String s);

    Item findOneByBarcode(String s);

    boolean findDuplicateBarcode(String s);

    /**
     * Update barcode for an item and all related records
     * @param itemId The ID of the item
     * @param oldBarcode The current barcode
     * @param newBarcode The new barcode
     * @return Response with success/failure message
     */
    Response updateBarcode(String itemId, String oldBarcode, String newBarcode);

    /**
     * Update item checkbox properties (wholesale, retail, manageStock, active)
     * @param itemId The ID of the item to update
     * @param wholesale Wholesale flag
     * @param retail Retail flag
     * @param manageStock Manage stock flag
     * @param active Active flag
     * @return Response with success/failure message
     */
    Response updateItemProperties(String itemId, Boolean wholesale, Boolean retail, Boolean manageStock, Boolean active);

    /**
     * Find all items with filters
     * @param pageable Pagination information
     * @param categoryId Category ID filter
     * @param brandId Brand ID filter
     * @param modelId Model ID filter
     * @param supplierId Supplier ID filter
     * @param wholesale Wholesale flag filter
     * @param retail Retail flag filter
     * @param manageStock Manage stock flag filter
     * @param active Active flag filter
     * @param sortBy Field to sort by
     * @param sortDirection Sort direction (asc/desc)
     * @param groupBy Field to group by
     * @return Page of filtered items
     */
    Iterable<Item> findAllFiltered(
            Pageable pageable,
            String categoryId,
            String brandId,
            String modelId,
            String supplierId,
            String itemTypeId,
            Boolean wholesale,
            Boolean retail,
            Boolean manageStock,
            Boolean active,
            String sortBy,
            String sortDirection,
            String groupBy);
}
