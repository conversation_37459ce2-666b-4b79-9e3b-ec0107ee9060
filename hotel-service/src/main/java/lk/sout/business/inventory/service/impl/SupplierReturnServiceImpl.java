package lk.sout.business.inventory.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.SequenceService;
import lk.sout.business.inventory.entity.Stock;
import lk.sout.business.inventory.entity.SupplierReturn;
import lk.sout.business.inventory.repository.SupplierReturnRepository;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.inventory.service.SupplierReturnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class SupplierReturnServiceImpl implements SupplierReturnService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierReturnServiceImpl.class);

    @Autowired
    private SupplierReturnRepository supplierReturnRepository;
    
    @Autowired
    private StockService stockService;
    
    @Autowired
    private SequenceService sequenceService;
    
    @Autowired
    private Response response;

    @Override
    @Transactional
    public Response save(SupplierReturn supplierReturn) {
        try {
            // Generate return number for new returns
            if (supplierReturn.getId() == null) {
                Sequence sequence = sequenceService.findSequenceByName("SupplierReturnNo");
                if (sequence != null) {
                    String returnNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                    supplierReturn.setReturnNo(returnNo);
                    
                    // Increment the sequence counter
                    sequence.setCounter(sequence.getCounter() + 1);
                    sequenceService.save(sequence);
                } else {
                    LOGGER.error("Sequence not found for SupplierReturnNo");
                    response.setCode(500);
                    response.setSuccess(false);
                    response.setMessage("Failed to generate return number");
                    return response;
                }
            }
            
            // Deduct from stock
            Stock stock = stockService.findByItemCodeAndWarehouseAndPrice(
                    supplierReturn.getItemCode(), 
                    supplierReturn.getWarehouseCode(), 
                    supplierReturn.getSellingPrice());
            
            if (stock != null) {
                // Check if there's enough stock to return
                if (stock.getQuantity() < supplierReturn.getQuantity()) {
                    response.setCode(400);
                    response.setSuccess(false);
                    response.setMessage("Not enough stock to return");
                    return response;
                }
                
                // Deduct the quantity from stock
                stock.setQuantity(stock.getQuantity() - supplierReturn.getQuantity());
                stockService.save(stock, "Supplier Return: " + supplierReturn.getReturnNo(), supplierReturn.getItemCode());
            } else {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Stock not found");
                return response;
            }
            
            // Save the supplier return
            supplierReturn.setReturnDate(LocalDateTime.now());
            SupplierReturn savedReturn = supplierReturnRepository.save(supplierReturn);
            
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Supplier return saved successfully");
            response.setData(savedReturn.getReturnNo());
            return response;
        } catch (Exception e) {
            LOGGER.error("Error saving supplier return: " + e.getMessage(), e);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to save supplier return: " + e.getMessage());
            return response;
        }
    }

    @Override
    public Page<SupplierReturn> findAll(Pageable pageable) {
        try {
            return supplierReturnRepository.findAll(pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding all supplier returns: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SupplierReturn> findBySupplierCode(String supplierCode) {
        try {
            return supplierReturnRepository.findBySupplierCode(supplierCode);
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by supplier code: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SupplierReturn> findByItemCode(String itemCode) {
        try {
            return supplierReturnRepository.findByItemCode(itemCode);
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by item code: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SupplierReturn> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return supplierReturnRepository.findByReturnDateBetween(startDate, endDate);
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SupplierReturn> findBySupplierAndDateRange(String supplierCode, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return supplierReturnRepository.findBySupplierCodeAndReturnDateBetween(supplierCode, startDate, endDate);
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by supplier and date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SupplierReturn findById(String id) {
        try {
            Optional<SupplierReturn> supplierReturn = supplierReturnRepository.findById(id);
            return supplierReturn.orElse(null);
        } catch (Exception e) {
            LOGGER.error("Error finding supplier return by id: " + e.getMessage(), e);
            return null;
        }
    }
}
