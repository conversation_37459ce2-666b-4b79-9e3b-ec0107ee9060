package lk.sout.business.inventory.repository;

import lk.sout.business.inventory.entity.Brand;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 4/18/2018
 */
@Repository
public interface BrandRepository extends MongoRepository<Brand, String> {

    List<Brand> findByNameLikeIgnoreCaseAndActive(String key, Boolean active);

    Page<Brand> findAllByOrderByIdAsc(Pageable pageable);

}
