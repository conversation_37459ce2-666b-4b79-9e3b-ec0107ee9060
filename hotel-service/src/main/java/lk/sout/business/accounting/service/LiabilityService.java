package lk.sout.business.accounting.service;

import lk.sout.business.accounting.entity.Liability;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface LiabilityService {
    
    boolean save(Liability liability);
    
    Iterable<Liability> findAll(Pageable pageable);
    
    Liability findById(String id);
    
    List<Liability> findByType(String liabilityType);
    
    List<Liability> findByStatus(String status);
    
    List<Liability> findByCategory(String category);
    
    List<Liability> findCurrentLiabilities();
    
    List<Liability> findLongTermLiabilities();
    
    List<Liability> findActiveLiabilities();
    
    List<Liability> findOverdueLiabilities();
    
    List<Liability> findUpcomingPayments(int days);
    
    boolean makePayment(String id, Double paymentAmount, Date paymentDate, String notes);
    
    Double getTotalLiabilityValue();
    
    Map<String, Double> getLiabilitiesByTypeValue();
    
    boolean calculateInterest(String id);
    
    Double getTotalCurrentLiabilitiesValue();
    
    Double getTotalLongTermLiabilitiesValue();
    
    boolean markAsOverdue(String id);
    
    boolean markAsPaid(String id);
}
