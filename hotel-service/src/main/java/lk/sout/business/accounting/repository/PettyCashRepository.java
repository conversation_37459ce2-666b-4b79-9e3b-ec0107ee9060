package lk.sout.business.accounting.repository;

import lk.sout.business.accounting.entity.PettyCash;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PettyCashRepository extends MongoRepository<PettyCash, String> {

    List<PettyCash> findAllByActiveTrue();

    PettyCash findByPettyCashNo(String pettyCashNo);

    List<PettyCash> findByLocationContainingIgnoreCase(String location);

    List<PettyCash> findByCustodianContainingIgnoreCase(String custodian);
}
