package lk.sout.business.accounting.service;

import lk.sout.business.accounting.entity.PettyCash;
import lk.sout.business.accounting.entity.PettyCashTransaction;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

public interface PettyCashService {

    // Petty Cash Management
    Response save(PettyCash pettyCash);

    PettyCash basicSave(PettyCash pettyCash);

    List<PettyCash> findAllActive();

    PettyCash findByPettyCashNo(String pettyCashNo);

    PettyCash findById(String id);

    List<PettyCash> findByLocation(String location);

    List<PettyCash> findByCustodian(String custodian);

    // Petty Cash Transaction Management
    Response createTransaction(PettyCashTransaction transaction);

    Response replenishPettyCash(String pettyCashId, Double amount, String description, String voucherNo);

    Response recordExpense(String pettyCashId, Double amount, String description, 
                          String expenseCategoryId, String voucherNo, String receivedBy);

    Response recordRefund(String pettyCashId, Double amount, String description, String voucherNo);

    // Transaction Queries
    List<PettyCashTransaction> findTransactionsByPettyCash(String pettyCashId);

    List<PettyCashTransaction> findTransactionsByPettyCash(String pettyCashId, Pageable pageable);

    List<PettyCashTransaction> findTransactionsByDateRange(String pettyCashId, 
                                                          LocalDateTime startDate, LocalDateTime endDate);

    List<PettyCashTransaction> findTransactionsByType(String pettyCashId, String transactionTypeId);

    List<PettyCashTransaction> findTransactionsByExpenseCategory(String pettyCashId, String expenseCategoryId);

    List<PettyCashTransaction> findTransactionsByOperator(String pettyCashId, String operator);

    List<PettyCashTransaction> findAllTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    PettyCashTransaction findTransactionByVoucherNo(String voucherNo);

    // Balance Management
    Double getCurrentBalance(String pettyCashId);

    boolean updateBalance(String pettyCashId, Double amount, String operator);

    // Validation
    boolean validateSufficientBalance(String pettyCashId, Double amount);
}
