package lk.sout.business.accounting.service.impl;

import lk.sout.business.accounting.entity.Liability;
import lk.sout.business.accounting.repository.LiabilityRepository;
import lk.sout.business.accounting.service.LiabilityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class LiabilityServiceImpl implements LiabilityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiabilityServiceImpl.class);

    @Autowired
    private LiabilityRepository liabilityRepository;

    @Override
    public boolean save(Liability liability) {
        try {
            if (liability.getId() == null) {
                // New liability
                liability.setCreatedDate(new Date());
                liability.setActive(true);
                liability.setCurrentBalance(liability.getOriginalAmount());
            }
            
            liabilityRepository.save(liability);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving liability failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Liability> findAll(Pageable pageable) {
        try {
            return liabilityRepository.findByActiveTrue(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving liabilities failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Liability findById(String id) {
        try {
            Optional<Liability> liability = liabilityRepository.findById(id);
            return liability.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Retrieving liability by id failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findByType(String liabilityType) {
        try {
            return liabilityRepository.findByLiabilityTypeAndActiveTrue(liabilityType);
        } catch (Exception ex) {
            LOGGER.error("Retrieving liabilities by type failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findByStatus(String status) {
        try {
            return liabilityRepository.findByStatusAndActiveTrue(status);
        } catch (Exception ex) {
            LOGGER.error("Retrieving liabilities by status failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findByCategory(String category) {
        try {
            return liabilityRepository.findByCategoryAndActiveTrue(category);
        } catch (Exception ex) {
            LOGGER.error("Retrieving liabilities by category failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findCurrentLiabilities() {
        try {
            return liabilityRepository.findCurrentLiabilities();
        } catch (Exception ex) {
            LOGGER.error("Retrieving current liabilities failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findLongTermLiabilities() {
        try {
            return liabilityRepository.findLongTermLiabilities();
        } catch (Exception ex) {
            LOGGER.error("Retrieving long-term liabilities failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findActiveLiabilities() {
        try {
            return liabilityRepository.findActiveLiabilities();
        } catch (Exception ex) {
            LOGGER.error("Retrieving active liabilities failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findOverdueLiabilities() {
        try {
            return liabilityRepository.findOverdueLiabilitiesByDate(new Date());
        } catch (Exception ex) {
            LOGGER.error("Retrieving overdue liabilities failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Liability> findUpcomingPayments(int days) {
        try {
            Date startDate = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            cal.add(Calendar.DAY_OF_MONTH, days);
            Date endDate = cal.getTime();
            
            return liabilityRepository.findUpcomingPayments(startDate, endDate);
        } catch (Exception ex) {
            LOGGER.error("Retrieving upcoming payments failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean makePayment(String id, Double paymentAmount, Date paymentDate, String notes) {
        try {
            Optional<Liability> optionalLiability = liabilityRepository.findById(id);
            if (optionalLiability.isPresent()) {
                Liability liability = optionalLiability.get();
                
                // Update current balance
                Double newBalance = liability.getCurrentBalance() - paymentAmount;
                liability.setCurrentBalance(Math.max(0, newBalance));
                liability.setLastPaymentDate(paymentDate);
                
                // Mark as paid if balance is zero
                if (newBalance <= 0) {
                    liability.setStatus("PAID");
                }
                
                // Update description with payment notes
                if (notes != null && !notes.isEmpty()) {
                    liability.setDescription(liability.getDescription() + " | Payment: " + notes);
                }
                
                liabilityRepository.save(liability);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Making payment failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Double getTotalLiabilityValue() {
        try {
            List<Liability> liabilities = liabilityRepository.findAllActiveLiabilities();
            return liabilities.stream()
                    .mapToDouble(liability -> liability.getCurrentBalance() != null ? liability.getCurrentBalance() : 0.0)
                    .sum();
        } catch (Exception ex) {
            LOGGER.error("Calculating total liability value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Map<String, Double> getLiabilitiesByTypeValue() {
        try {
            Map<String, Double> result = new HashMap<>();
            result.put("CURRENT", getTotalCurrentLiabilitiesValue());
            result.put("LONG_TERM", getTotalLongTermLiabilitiesValue());
            return result;
        } catch (Exception ex) {
            LOGGER.error("Getting liabilities by type value failed: " + ex.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public boolean calculateInterest(String id) {
        try {
            Optional<Liability> optionalLiability = liabilityRepository.findById(id);
            if (optionalLiability.isPresent()) {
                Liability liability = optionalLiability.get();
                
                if (liability.getInterestRate() != null && liability.getInterestRate() > 0) {
                    // Simple interest calculation (can be enhanced)
                    Double interestAmount = liability.getCurrentBalance() * (liability.getInterestRate() / 100) / 12;
                    liability.setCurrentBalance(liability.getCurrentBalance() + interestAmount);
                    liabilityRepository.save(liability);
                }
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Calculating interest failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Double getTotalCurrentLiabilitiesValue() {
        try {
            List<Liability> currentLiabilities = findCurrentLiabilities();
            if (currentLiabilities != null) {
                return currentLiabilities.stream()
                        .mapToDouble(liability -> liability.getCurrentBalance() != null ? liability.getCurrentBalance() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total current liabilities value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Double getTotalLongTermLiabilitiesValue() {
        try {
            List<Liability> longTermLiabilities = findLongTermLiabilities();
            if (longTermLiabilities != null) {
                return longTermLiabilities.stream()
                        .mapToDouble(liability -> liability.getCurrentBalance() != null ? liability.getCurrentBalance() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total long-term liabilities value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public boolean markAsOverdue(String id) {
        try {
            Optional<Liability> optionalLiability = liabilityRepository.findById(id);
            if (optionalLiability.isPresent()) {
                Liability liability = optionalLiability.get();
                liability.setStatus("OVERDUE");
                liabilityRepository.save(liability);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Marking liability as overdue failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean markAsPaid(String id) {
        try {
            Optional<Liability> optionalLiability = liabilityRepository.findById(id);
            if (optionalLiability.isPresent()) {
                Liability liability = optionalLiability.get();
                liability.setStatus("PAID");
                liability.setCurrentBalance(0.0);
                liability.setLastPaymentDate(new Date());
                liabilityRepository.save(liability);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Marking liability as paid failed: " + ex.getMessage());
            return false;
        }
    }
}
