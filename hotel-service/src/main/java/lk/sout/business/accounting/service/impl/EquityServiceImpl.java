package lk.sout.business.accounting.service.impl;

import lk.sout.business.accounting.entity.Equity;
import lk.sout.business.accounting.repository.EquityRepository;
import lk.sout.business.accounting.service.EquityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EquityServiceImpl implements EquityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EquityServiceImpl.class);

    @Autowired
    private EquityRepository equityRepository;

    @Override
    public boolean save(Equity equity) {
        try {
            if (equity.getId() == null) {
                // New equity transaction
                equity.setCreatedDate(new Date());
                equity.setActive(true);
                equity.setStatus("ACTIVE");
                
                // Generate reference number if not provided
                if (equity.getReferenceNumber() == null || equity.getReferenceNumber().isEmpty()) {
                    equity.setReferenceNumber("EQ" + System.currentTimeMillis());
                }
            }
            
            equityRepository.save(equity);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving equity failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Equity> findAll(Pageable pageable) {
        try {
            return equityRepository.findByActiveTrue(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving equity transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Equity findById(String id) {
        try {
            Optional<Equity> equity = equityRepository.findById(id);
            return equity.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Retrieving equity by id failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findByType(String equityType) {
        try {
            return equityRepository.findByEquityTypeAndActiveTrue(equityType);
        } catch (Exception ex) {
            LOGGER.error("Retrieving equity by type failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findByFiscalYear(String fiscalYear) {
        try {
            return equityRepository.findByFiscalYearAndActiveTrue(fiscalYear);
        } catch (Exception ex) {
            LOGGER.error("Retrieving equity by fiscal year failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findByOwner(String ownerName) {
        try {
            return equityRepository.findByOwnerNameAndActiveTrue(ownerName);
        } catch (Exception ex) {
            LOGGER.error("Retrieving equity by owner failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findCapitalTransactions() {
        try {
            return equityRepository.findCapitalTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving capital transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findRetainedEarningsTransactions() {
        try {
            return equityRepository.findRetainedEarningsTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving retained earnings transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findDrawingsTransactions() {
        try {
            return equityRepository.findDrawingsTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving drawings transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findInvestmentTransactions() {
        try {
            return equityRepository.findInvestmentTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving investment transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findWithdrawalTransactions() {
        try {
            return equityRepository.findWithdrawalTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving withdrawal transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Equity> findProfitRetentionTransactions() {
        try {
            return equityRepository.findProfitRetentionTransactions();
        } catch (Exception ex) {
            LOGGER.error("Retrieving profit retention transactions failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Double getTotalEquityValue() {
        try {
            List<Equity> allEquity = equityRepository.findAllActiveEquity();
            return allEquity.stream()
                    .mapToDouble(equity -> {
                        double amount = equity.getAmount() != null ? equity.getAmount() : 0.0;
                        // Withdrawals and drawings reduce equity
                        if ("WITHDRAWAL".equals(equity.getTransactionType()) || 
                            "DRAWINGS".equals(equity.getEquityType())) {
                            return -amount;
                        }
                        return amount;
                    })
                    .sum();
        } catch (Exception ex) {
            LOGGER.error("Calculating total equity value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Map<String, Double> getEquityByTypeValue() {
        try {
            Map<String, Double> result = new HashMap<>();
            result.put("CAPITAL", getTotalCapital());
            result.put("RETAINED_EARNINGS", getRetainedEarnings());
            result.put("DRAWINGS", getTotalDrawings());
            return result;
        } catch (Exception ex) {
            LOGGER.error("Getting equity by type value failed: " + ex.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public Double getRetainedEarnings() {
        try {
            List<Equity> retainedEarnings = findRetainedEarningsTransactions();
            if (retainedEarnings != null) {
                return retainedEarnings.stream()
                        .mapToDouble(equity -> equity.getAmount() != null ? equity.getAmount() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating retained earnings failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Double getTotalCapital() {
        try {
            List<Equity> capitalTransactions = findCapitalTransactions();
            if (capitalTransactions != null) {
                return capitalTransactions.stream()
                        .mapToDouble(equity -> {
                            double amount = equity.getAmount() != null ? equity.getAmount() : 0.0;
                            // Withdrawals reduce capital
                            if ("WITHDRAWAL".equals(equity.getTransactionType())) {
                                return -amount;
                            }
                            return amount;
                        })
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total capital failed: " + ex.getMessage());
            return 0.0;
        }
    }

    private Double getTotalDrawings() {
        try {
            List<Equity> drawings = findDrawingsTransactions();
            if (drawings != null) {
                return drawings.stream()
                        .mapToDouble(equity -> equity.getAmount() != null ? equity.getAmount() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total drawings failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public boolean recordProfitRetention(Double amount, String fiscalYear, String notes) {
        try {
            Equity equity = new Equity();
            equity.setEquityType("RETAINED_EARNINGS");
            equity.setTransactionType("PROFIT_RETENTION");
            equity.setAmount(amount);
            equity.setFiscalYear(fiscalYear);
            equity.setNotes(notes);
            equity.setDescription("Profit retention for fiscal year " + fiscalYear);
            equity.setTransactionDate(new Date());
            
            return save(equity);
        } catch (Exception ex) {
            LOGGER.error("Recording profit retention failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean recordOwnerDrawing(Double amount, String ownerName, String notes) {
        try {
            Equity equity = new Equity();
            equity.setEquityType("DRAWINGS");
            equity.setTransactionType("WITHDRAWAL");
            equity.setAmount(amount);
            equity.setOwnerName(ownerName);
            equity.setNotes(notes);
            equity.setDescription("Owner drawing by " + ownerName);
            equity.setTransactionDate(new Date());
            equity.setFiscalYear(String.valueOf(new Date().getYear() + 1900));
            
            return save(equity);
        } catch (Exception ex) {
            LOGGER.error("Recording owner drawing failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Double calculateNetWorth() {
        try {
            return getTotalEquityValue();
        } catch (Exception ex) {
            LOGGER.error("Calculating net worth failed: " + ex.getMessage());
            return 0.0;
        }
    }
}
