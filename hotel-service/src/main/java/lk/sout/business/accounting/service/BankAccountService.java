package lk.sout.business.accounting.service;

import lk.sout.business.accounting.entity.BankAccount;
import lk.sout.business.accounting.entity.BankAccountTransaction;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;

public interface BankAccountService {

    // Bank Account Management
    Response save(BankAccount bankAccount);

    BankAccount basicSave(BankAccount bankAccount);

    List<BankAccount> findAllActive();

    BankAccount findById(String id);

    BankAccount findByAccountNo(String accountNo);

    List<BankAccount> findByBankName(String bankName);

    List<BankAccount> findByAccountHolderName(String accountHolderName);

    List<BankAccount> findByAccountType(String accountType);

    Page<BankAccount> findAllPage(int page, int pageSize);

    // Bank Account Transaction Management
    Response createTransaction(BankAccountTransaction transaction);

    BankAccountTransaction basicSaveTransaction(BankAccountTransaction transaction);

    List<BankAccountTransaction> findTransactionsByBankAccount(String bankAccountId);

    List<BankAccountTransaction> findTransactionsByReferenceNo(String referenceNo);

    List<BankAccountTransaction> findTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    List<BankAccountTransaction> findTransactionsByBankAccountAndDateRange(
            String bankAccountId, LocalDateTime startDate, LocalDateTime endDate);

    Page<BankAccountTransaction> findTransactionsPage(String bankAccountId, int page, int pageSize);

    // Balance Management
    Double getCurrentBalance(String bankAccountId);

    boolean updateBalance(String bankAccountId, Double amount, String operator);

    boolean debitAccount(String bankAccountId, Double amount, String referenceNo, 
                        String referenceType, String description, String thirdParty);

    boolean creditAccount(String bankAccountId, Double amount, String referenceNo, 
                         String referenceType, String description, String thirdParty);

    // Purchase Invoice Payment
    boolean processPurchasePayment(String bankAccountId, Double amount, String purchaseInvoiceNo, String supplierName);

    // Balance Correction
    Response correctBankBalance(String bankAccountId, Double newBalance, String reason, String correctedBy);

    // Utility Methods
    BankAccountTransaction getLastTransaction(String bankAccountId);

    Double calculateBalanceFromTransactions(String bankAccountId);

    boolean validateSufficientBalance(String bankAccountId, Double amount);
}
