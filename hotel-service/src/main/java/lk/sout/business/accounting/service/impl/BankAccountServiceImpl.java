package lk.sout.business.accounting.service.impl;

import lk.sout.business.accounting.entity.BankAccount;
import lk.sout.business.accounting.entity.BankAccountTransaction;
import lk.sout.business.accounting.service.BankAccountService;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Action;
import lk.sout.core.entity.MetaData;
import lk.sout.business.accounting.repository.BankAccountRepository;
import lk.sout.business.accounting.repository.BankAccountTransactionRepository;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class BankAccountServiceImpl implements BankAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankAccountServiceImpl.class);

    @Autowired
    private BankAccountRepository bankAccountRepository;

    @Autowired
    private BankAccountTransactionRepository bankAccountTransactionRepository;

    @Autowired
    private ActionService actionService;

    @Autowired
    private MetaDataService metaDataService;

    // Bank Account Management

    @Override
    @Transactional
    public Response save(BankAccount bankAccount) {
        Response response = new Response();
        try {
            if (bankAccount.getId() == null || bankAccount.getId().isEmpty()) {
                // New bank account
                bankAccount.setActive(true);
                if (bankAccount.getCurrentBalance() == null) {
                    bankAccount.setCurrentBalance(bankAccount.getOpeningBalance() != null ?
                            bankAccount.getOpeningBalance() : 0.0);
                }
            }

            BankAccount savedAccount = bankAccountRepository.save(bankAccount);

            // Create opening balance transaction for new accounts
            if (bankAccount.getId() == null && bankAccount.getOpeningBalance() != null && bankAccount.getOpeningBalance() > 0) {
                BankAccountTransaction openingTransaction = new BankAccountTransaction();
                openingTransaction.setBankAccount(savedAccount);
                openingTransaction.setTransactionType(BankAccountTransaction.TRANSACTION_TYPE_CREDIT);
                openingTransaction.setPaymentType("OPENING_BALANCE");
                openingTransaction.setAmount(bankAccount.getOpeningBalance());
                openingTransaction.setReferenceNo("OPENING");
                openingTransaction.setReferenceType("OPENING_BALANCE");
                openingTransaction.setDescription("Opening balance");
                openingTransaction.setTransactionDate(LocalDateTime.now());
                openingTransaction.setBalanceAfterTransaction(bankAccount.getOpeningBalance());
                openingTransaction.setActive(true);
                bankAccountTransactionRepository.save(openingTransaction);
            }

            response.setCode(200);
            response.setMessage("Bank account saved successfully");
            response.setSuccess(true);
        } catch (Exception e) {
            LOGGER.error("Error saving bank account: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error saving bank account: " + e.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public BankAccount basicSave(BankAccount bankAccount) {
        try {
            return bankAccountRepository.save(bankAccount);
        } catch (Exception e) {
            LOGGER.error("Error in basic save: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccount> findAllActive() {
        try {
            return bankAccountRepository.findAllByActiveTrue();
        } catch (Exception e) {
            LOGGER.error("Error finding active bank accounts: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public BankAccount findById(String id) {
        try {
            return bankAccountRepository.findByIdAndActiveTrue(id);
        } catch (Exception e) {
            LOGGER.error("Error finding bank account by ID: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public BankAccount findByAccountNo(String accountNo) {
        try {
            return bankAccountRepository.findByAccountNo(accountNo);
        } catch (Exception e) {
            LOGGER.error("Error finding bank account by account number: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccount> findByBankName(String bankName) {
        try {
            return bankAccountRepository.findByBankNameContainingIgnoreCaseAndActiveTrue(bankName);
        } catch (Exception e) {
            LOGGER.error("Error finding bank accounts by bank name: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccount> findByAccountHolderName(String accountHolderName) {
        try {
            return bankAccountRepository.findByAccountHolderNameContainingIgnoreCaseAndActiveTrue(accountHolderName);
        } catch (Exception e) {
            LOGGER.error("Error finding bank accounts by account holder name: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccount> findByAccountType(String accountType) {
        try {
            return bankAccountRepository.findByAccountTypeAndActiveTrue(accountType);
        } catch (Exception e) {
            LOGGER.error("Error finding bank accounts by account type: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<BankAccount> findAllPage(int page, int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("createdDate").descending());
            return bankAccountRepository.findAllByActiveTrue(pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding bank accounts page: " + e.getMessage(), e);
            return null;
        }
    }

    // Bank Account Transaction Management

    @Override
    @Transactional
    public Response createTransaction(BankAccountTransaction transaction) {
        Response response = new Response();
        try {
            transaction.setActive(true);
            transaction.setTransactionDate(LocalDateTime.now());

            // Calculate balance after transaction
            BankAccount bankAccount = transaction.getBankAccount();
            Double currentBalance = getCurrentBalance(bankAccount.getId());
            Double newBalance;

            if (BankAccountTransaction.TRANSACTION_TYPE_CREDIT.equals(transaction.getTransactionType())) {
                newBalance = currentBalance + transaction.getAmount();
            } else {
                newBalance = currentBalance - transaction.getAmount();
                if (newBalance < 0) {
                    response.setCode(400);
                    response.setMessage("Insufficient balance");
                    response.setSuccess(false);
                    return response;
                }
            }

            transaction.setBalanceAfterTransaction(newBalance);
            bankAccountTransactionRepository.save(transaction);

            // Update bank account balance
            bankAccount.setCurrentBalance(newBalance);
            bankAccountRepository.save(bankAccount);

            response.setCode(200);
            response.setMessage("Transaction created successfully");
            response.setSuccess(true);
        } catch (Exception e) {
            LOGGER.error("Error creating transaction: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error creating transaction: " + e.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public BankAccountTransaction basicSaveTransaction(BankAccountTransaction transaction) {
        try {
            return bankAccountTransactionRepository.save(transaction);
        } catch (Exception e) {
            LOGGER.error("Error in basic save transaction: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccountTransaction> findTransactionsByBankAccount(String bankAccountId) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                return bankAccountTransactionRepository.findByBankAccountAndActiveTrueOrderByTransactionDateDesc(bankAccount);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by bank account: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccountTransaction> findTransactionsByReferenceNo(String referenceNo) {
        try {
            return bankAccountTransactionRepository.findByReferenceNoAndActiveTrue(referenceNo);
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by reference number: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccountTransaction> findTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return bankAccountTransactionRepository.findByTransactionDateBetweenAndActiveTrue(startDate, endDate);
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<BankAccountTransaction> findTransactionsByBankAccountAndDateRange(
            String bankAccountId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                return bankAccountTransactionRepository.findByBankAccountAndTransactionDateBetweenAndActiveTrue(
                        bankAccount, startDate, endDate);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by bank account and date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<BankAccountTransaction> findTransactionsPage(String bankAccountId, int page, int pageSize) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                Pageable pageable = PageRequest.of(page, pageSize, Sort.by("transactionDate").descending());
                return bankAccountTransactionRepository.findByBankAccountAndActiveTrue(bankAccount, pageable);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions page: " + e.getMessage(), e);
            return null;
        }
    }

    // Balance Management

    @Override
    public Double getCurrentBalance(String bankAccountId) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            return bankAccount != null ? bankAccount.getCurrentBalance() : 0.0;
        } catch (Exception e) {
            LOGGER.error("Error getting current balance: " + e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    @Transactional
    public boolean updateBalance(String bankAccountId, Double amount, String operator) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                Double currentBalance = bankAccount.getCurrentBalance();
                Double newBalance;

                if ("ADD".equals(operator)) {
                    newBalance = currentBalance + amount;
                } else if ("SUBTRACT".equals(operator)) {
                    newBalance = currentBalance - amount;
                    if (newBalance < 0) {
                        return false; // Insufficient balance
                    }
                } else {
                    return false; // Invalid operator
                }

                bankAccount.setCurrentBalance(newBalance);
                bankAccountRepository.save(bankAccount);
                return true;
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("Error updating balance: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean debitAccount(String bankAccountId, Double amount, String referenceNo,
                                String referenceType, String description, String thirdParty) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null && validateSufficientBalance(bankAccountId, amount)) {
                BankAccountTransaction transaction = new BankAccountTransaction();
                transaction.setBankAccount(bankAccount);
                transaction.setTransactionType(BankAccountTransaction.TRANSACTION_TYPE_DEBIT);
                transaction.setPaymentType(BankAccountTransaction.PAYMENT_TYPE_PURCHASE_PAYMENT);
                transaction.setAmount(amount);
                transaction.setReferenceNo(referenceNo);
                transaction.setReferenceType(referenceType);
                transaction.setDescription(description);
                transaction.setThirdParty(thirdParty);

                Response response = createTransaction(transaction);
                return response.isSuccess();
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("Error debiting account: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean creditAccount(String bankAccountId, Double amount, String referenceNo,
                                 String referenceType, String description, String thirdParty) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                BankAccountTransaction transaction = new BankAccountTransaction();
                transaction.setBankAccount(bankAccount);
                transaction.setTransactionType(BankAccountTransaction.TRANSACTION_TYPE_CREDIT);
                transaction.setPaymentType(BankAccountTransaction.PAYMENT_TYPE_DEPOSIT);
                transaction.setAmount(amount);
                transaction.setReferenceNo(referenceNo);
                transaction.setReferenceType(referenceType);
                transaction.setDescription(description);
                transaction.setThirdParty(thirdParty);

                Response response = createTransaction(transaction);
                return response.isSuccess();
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("Error crediting account: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean processPurchasePayment(String bankAccountId, Double amount, String purchaseInvoiceNo, String supplierName) {
        try {
            return debitAccount(bankAccountId, amount, purchaseInvoiceNo,
                    BankAccountTransaction.REFERENCE_TYPE_PURCHASE_INVOICE,
                    "Purchase Invoice Payment - " + purchaseInvoiceNo, supplierName);
        } catch (Exception e) {
            LOGGER.error("Error processing purchase payment: " + e.getMessage(), e);
            return false;
        }
    }

    // Utility Methods

    @Override
    public BankAccountTransaction getLastTransaction(String bankAccountId) {
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount != null) {
                return bankAccountTransactionRepository.findTopByBankAccountAndActiveTrueOrderByTransactionDateDesc(bankAccount);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("Error getting last transaction: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Double calculateBalanceFromTransactions(String bankAccountId) {
        try {
            List<BankAccountTransaction> transactions = findTransactionsByBankAccount(bankAccountId);
            if (transactions != null && !transactions.isEmpty()) {
                return transactions.get(0).getBalanceAfterTransaction();
            }
            return 0.0;
        } catch (Exception e) {
            LOGGER.error("Error calculating balance from transactions: " + e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    public boolean validateSufficientBalance(String bankAccountId, Double amount) {
        try {
            Double currentBalance = getCurrentBalance(bankAccountId);
            return currentBalance >= amount;
        } catch (Exception e) {
            LOGGER.error("Error validating sufficient balance: " + e.getMessage(), e);
            return false;
        }
    }

    // Balance Correction

    @Override
    @Transactional
    public Response correctBankBalance(String bankAccountId, Double newBalance, String reason, String correctedBy) {
        Response response = new Response();
        try {
            BankAccount bankAccount = findById(bankAccountId);
            if (bankAccount == null) {
                response.setCode(404);
                response.setMessage("Bank account not found");
                response.setSuccess(false);
                return response;
            }

            Double oldBalance = bankAccount.getCurrentBalance();
            Double difference = newBalance - oldBalance;

            // Update bank account balance
            bankAccount.setCurrentBalance(newBalance);
            bankAccountRepository.save(bankAccount);

            // Create balance correction transaction
            BankAccountTransaction correctionTransaction = new BankAccountTransaction();
            correctionTransaction.setBankAccount(bankAccount);
            correctionTransaction.setTransactionType(difference >= 0 ?
                    BankAccountTransaction.TRANSACTION_TYPE_CREDIT :
                    BankAccountTransaction.TRANSACTION_TYPE_DEBIT);
            correctionTransaction.setPaymentType("BALANCE_CORRECTION");
            correctionTransaction.setAmount(Math.abs(difference));
            correctionTransaction.setReferenceNo("BAL_CORR_" + System.currentTimeMillis());
            correctionTransaction.setReferenceType("BALANCE_CORRECTION");
            correctionTransaction.setDescription("Balance correction: " + reason);
            correctionTransaction.setThirdParty(correctedBy);
            correctionTransaction.setTransactionDate(LocalDateTime.now());
            correctionTransaction.setBalanceAfterTransaction(newBalance);
            correctionTransaction.setActive(true);
            bankAccountTransactionRepository.save(correctionTransaction);

            // Record action for audit trail
            try {
                MetaData actionType = metaDataService.searchMetaData("Bank Balance Changes", "Action");
                if (actionType != null) {
                    Action action = new Action();
                    action.setType(actionType.getName());
                    action.setRemark(String.format("Bank balance corrected for %s (%s). Old balance: %.2f, New balance: %.2f, Difference: %.2f. Reason: %s",
                            bankAccount.getBankName(), bankAccount.getAccountNo(), oldBalance, newBalance, difference, reason));
                    action.setCreatedBy(correctedBy);
                    action.setCreatedDate(LocalDateTime.now());
                    action.setChange(String.valueOf(difference));
                    actionService.save(action);
                }
            } catch (Exception actionEx) {
                LOGGER.warn("Failed to record action for balance correction: " + actionEx.getMessage());
                // Don't fail the entire operation if action recording fails
            }

            response.setCode(200);
            response.setMessage("Bank balance corrected successfully");
            response.setSuccess(true);

        } catch (Exception e) {
            LOGGER.error("Error correcting bank balance: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error correcting bank balance: " + e.getMessage());
            response.setSuccess(false);
        }
        return response;
    }
}
