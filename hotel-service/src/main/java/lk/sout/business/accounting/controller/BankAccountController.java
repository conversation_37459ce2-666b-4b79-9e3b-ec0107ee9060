package lk.sout.business.accounting.controller;

import lk.sout.business.accounting.entity.BankAccount;
import lk.sout.business.accounting.entity.BankAccountTransaction;
import lk.sout.core.entity.Response;
import lk.sout.business.accounting.service.BankAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/bankAccount")
@CrossOrigin
public class BankAccountController {

    @Autowired
    private BankAccountService bankAccountService;

    // Bank Account Management Endpoints

    @PostMapping("/save")
    public Response save(@RequestBody BankAccount bankAccount) {
        return bankAccountService.save(bankAccount);
    }

    @GetMapping("/findAllBankAccounts")
    public List<BankAccount> findAllBankAccounts() {
        return bankAccountService.findAllActive();
    }

    @GetMapping("/findById")
    public BankAccount findById(@RequestParam String id) {
        return bankAccountService.findById(id);
    }

    @GetMapping("/search")
    public BankAccount findByAccountNo(@RequestParam String any) {
        return bankAccountService.findByAccountNo(any);
    }

    @GetMapping("/findAllPage")
    public Page<BankAccount> findAllPage(@RequestParam int page, @RequestParam int pageSize) {
        return bankAccountService.findAllPage(page, pageSize);
    }

    @GetMapping("/findByBankName")
    public List<BankAccount> findByBankName(@RequestParam String bankName) {
        return bankAccountService.findByBankName(bankName);
    }

    @GetMapping("/findByAccountHolderName")
    public List<BankAccount> findByAccountHolderName(@RequestParam String accountHolderName) {
        return bankAccountService.findByAccountHolderName(accountHolderName);
    }

    @GetMapping("/findByAccountType")
    public List<BankAccount> findByAccountType(@RequestParam String accountType) {
        return bankAccountService.findByAccountType(accountType);
    }

    // Bank Account Transaction Endpoints

    @PostMapping("/createTransaction")
    public Response createTransaction(@RequestBody BankAccountTransaction transaction) {
        return bankAccountService.createTransaction(transaction);
    }

    @GetMapping("/findTransactionsByBankAccount")
    public List<BankAccountTransaction> findTransactionsByBankAccount(@RequestParam String bankAccountId) {
        return bankAccountService.findTransactionsByBankAccount(bankAccountId);
    }

    @GetMapping("/findTransactionsByReferenceNo")
    public List<BankAccountTransaction> findTransactionsByReferenceNo(@RequestParam String referenceNo) {
        return bankAccountService.findTransactionsByReferenceNo(referenceNo);
    }

    @GetMapping("/findTransactionsByDateRange")
    public List<BankAccountTransaction> findTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return bankAccountService.findTransactionsByDateRange(startDate, endDate);
    }

    @GetMapping("/findTransactionsByBankAccountAndDateRange")
    public List<BankAccountTransaction> findTransactionsByBankAccountAndDateRange(
            @RequestParam String bankAccountId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return bankAccountService.findTransactionsByBankAccountAndDateRange(bankAccountId, startDate, endDate);
    }

    @GetMapping("/findTransactionsPage")
    public Page<BankAccountTransaction> findTransactionsPage(
            @RequestParam String bankAccountId,
            @RequestParam int page,
            @RequestParam int pageSize) {
        return bankAccountService.findTransactionsPage(bankAccountId, page, pageSize);
    }

    // Balance Management Endpoints

    @GetMapping("/getCurrentBalance")
    public Double getCurrentBalance(@RequestParam String bankAccountId) {
        return bankAccountService.getCurrentBalance(bankAccountId);
    }

    @PostMapping("/updateBalance")
    public boolean updateBalance(@RequestParam String bankAccountId, 
                                @RequestParam Double amount, 
                                @RequestParam String operator) {
        return bankAccountService.updateBalance(bankAccountId, amount, operator);
    }

    @PostMapping("/debitAccount")
    public boolean debitAccount(@RequestParam String bankAccountId,
                               @RequestParam Double amount,
                               @RequestParam String referenceNo,
                               @RequestParam String referenceType,
                               @RequestParam String description,
                               @RequestParam(required = false) String thirdParty) {
        return bankAccountService.debitAccount(bankAccountId, amount, referenceNo, referenceType, description, thirdParty);
    }

    @PostMapping("/creditAccount")
    public boolean creditAccount(@RequestParam String bankAccountId,
                                @RequestParam Double amount,
                                @RequestParam String referenceNo,
                                @RequestParam String referenceType,
                                @RequestParam String description,
                                @RequestParam(required = false) String thirdParty) {
        return bankAccountService.creditAccount(bankAccountId, amount, referenceNo, referenceType, description, thirdParty);
    }

    @PostMapping("/processPurchasePayment")
    public boolean processPurchasePayment(@RequestParam String bankAccountId,
                                         @RequestParam Double amount,
                                         @RequestParam String purchaseInvoiceNo,
                                         @RequestParam String supplierName) {
        return bankAccountService.processPurchasePayment(bankAccountId, amount, purchaseInvoiceNo, supplierName);
    }

    // Utility Endpoints

    @GetMapping("/getLastTransaction")
    public BankAccountTransaction getLastTransaction(@RequestParam String bankAccountId) {
        return bankAccountService.getLastTransaction(bankAccountId);
    }

    @GetMapping("/calculateBalanceFromTransactions")
    public Double calculateBalanceFromTransactions(@RequestParam String bankAccountId) {
        return bankAccountService.calculateBalanceFromTransactions(bankAccountId);
    }

    @GetMapping("/validateSufficientBalance")
    public boolean validateSufficientBalance(@RequestParam String bankAccountId, @RequestParam Double amount) {
        return bankAccountService.validateSufficientBalance(bankAccountId, amount);
    }

    // Balance Correction Endpoint
    @PostMapping("/correctBalance")
    public Response correctBankBalance(@RequestParam String bankAccountId,
                                      @RequestParam Double newBalance,
                                      @RequestParam String reason,
                                      @RequestParam String correctedBy) {
        return bankAccountService.correctBankBalance(bankAccountId, newBalance, reason, correctedBy);
    }
}
