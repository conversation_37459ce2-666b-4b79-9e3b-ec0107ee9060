package lk.sout.business.trade.service.impl;

import lk.sout.business.trade.entity.PurchaseInvoiceRecord;
import lk.sout.business.trade.repository.PurchaseInvoiceRecordRepository;
import lk.sout.business.trade.service.PurchaseInvoiceRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class PurchaseInvoiceRecordServiceImpl implements PurchaseInvoiceRecordService {


    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseInvoiceRecordServiceImpl.class);

    @Autowired
    PurchaseInvoiceRecordRepository purchaseInvoiceRecordRepository;

    @Override
    public List<PurchaseInvoiceRecord> findAllByItemAndDate(String item, LocalDate sDate, LocalDate eDate) {
        try {
            return purchaseInvoiceRecordRepository.findAllByItemCodeAndDateBetween(item, sDate.atStartOfDay(), eDate.atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoiceRecord> findAllByItem(String item) {
        try {
            return purchaseInvoiceRecordRepository.findAllByItemCodeOrderByDateDesc(item);
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return null;
        }
    }

}
