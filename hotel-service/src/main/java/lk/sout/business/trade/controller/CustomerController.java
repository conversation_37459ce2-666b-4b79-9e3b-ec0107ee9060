package lk.sout.business.trade.controller;

import lk.sout.core.entity.Response;
import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.service.CustomerService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private Response save(@RequestBody Customer customer) {
        try {
            // Get response from service layer
            Response response = customerService.save(customer);

            // Always return the response object directly
            // It already contains success/failure status, message, and data
            return response;
        } catch (Exception e) {
            // In case of unexpected exceptions, still return a Response object
            // The service layer will have already logged the exception
            Response errorResponse = new Response();
            errorResponse.setSuccess(false);
            errorResponse.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
            errorResponse.setMessage("An unexpected error occurred");
            return errorResponse;
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public Iterable<Customer> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return customerService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize)));
        } catch (Exception e) {
            // Service layer will log the exception
            return null;
        }
    }


    @RequestMapping(value = "/searchByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private List<Customer> searchByName(@RequestParam String name) {
        try {
            return customerService.findAllByNameLikeIgnoreCaseAndActive(name, true);
        } catch (Exception e) {
            // Service layer will log the exception
            return new ArrayList<>();
        }
    }

    @RequestMapping(value = "/searchByNicLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private List<Customer> searchByNicLike(@RequestParam String nic) {
        try {
            return customerService.findByNicLike(nic);
        } catch (Exception e) {
            // Service layer will log the exception
            return new ArrayList<>();
        }
    }

    @RequestMapping(value = "/searchByTpLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private List<Customer> searchByTpLike(@RequestParam String tp) {
        try {
            return customerService.findByTpLike(tp);
        } catch (Exception e) {
            // Service layer will log the exception
            return new ArrayList<>();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private Customer findById(@RequestParam String id) {
        try {
            return customerService.findById(id);
        } catch (Exception e) {
            // Service layer will log the exception
            return null;
        }
    }

    @RequestMapping(value = "/checkNic", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private boolean checkNicAvailability(@RequestParam("nic") String nic) {
        try {
            return customerService.checkNic(nic);
        } catch (Exception e) {
            // Service layer will log the exception
            return false;
        }
    }

    /**
     * Find a customer by customer number
     * @param customerNo The customer number to search for
     * @return The customer with the given customer number, or null if not found
     */
    @RequestMapping(value = "/findByCustomerNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private Customer findByCustomerNo(@RequestParam("customerNo") String customerNo) {
        try {
            return customerService.findByCustomerNo(customerNo);
        } catch (Exception e) {
            // Service layer will log the exception
            return null;
        }
    }

}
