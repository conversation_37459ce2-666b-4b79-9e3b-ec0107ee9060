package lk.sout.business.trade.controller;

import lk.sout.business.trade.entity.PayBalance;
import lk.sout.business.trade.entity.SalesInvoice;
import lk.sout.business.trade.service.SalesInvoiceService;
import lk.sout.core.entity.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/salesInvoice")
public class SalesInvoiceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceController.class);

    @Autowired
    SalesInvoiceService salesInvoiceService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SalesInvoice salesInvoice) {
        try {
            return ResponseEntity.ok(salesInvoiceService.save(salesInvoice));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPages", method = RequestMethod.GET)
    private ResponseEntity<?> getAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAll(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingPages", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingPages(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllPendingPages(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllIncomplete", method = RequestMethod.GET)
    private ResponseEntity<?> findAllIncomplete() {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllBalanceGreaterThan(15.0));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCustomer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByCustomer(@RequestParam String nicBr) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByCustomerNicBr(nicBr));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCustomerId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByCustomerId(@RequestParam String id, @RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByCustomerId(id, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findPendingByCustomer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findPendingByCustomer(@RequestParam String nicBr) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByCustomerNicBr(nicBr));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByInvoiceNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByInvoiceNo(@RequestParam String invNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByInvNo(invNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByOrderNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByOrderNo(@RequestParam String orderNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByOrderNo(orderNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByPaymentMethod", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByPaymentMethod(@RequestParam String paymentMethodId,
                                                     @RequestParam String page,
                                                     @RequestParam String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllByPaymentMethod(paymentMethodId,
                    PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByPaymentStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByPaymentStatus(@RequestParam String paymentStatusId,
                                                     @RequestParam String page,
                                                     @RequestParam String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllByPaymentStatus(paymentStatusId,
                    PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByJobNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByJobNo(@RequestParam String jobNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByReference(jobNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/payBalance", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> payBalance(@RequestBody PayBalance payBalance) {
        try {
            return ResponseEntity.ok(salesInvoiceService.payBalance(payBalance));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByDate", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByDate(@RequestParam String date) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(salesInvoiceService.findByDate(LocalDate.parse(date, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/amendSi", method = RequestMethod.GET)
    private ResponseEntity<?> amendSi(@RequestParam("invNo") String invNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.amendSi(invNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Find sales invoices with multiple filters
     * @param page Page number (0-based)
     * @param pageSize Page size
     * @param startDate Start date (optional)
     * @param endDate End date (optional)
     * @param customerNo Customer number (optional)
     * @param invoiceNo Invoice number (optional)
     * @param drawerNo Cash drawer number (optional)
     * @param cashierUserName Cashier username (optional)
     * @param routeNo Route number (optional)
     * @return Page of filtered sales invoices
     */
    @RequestMapping(value = "/findWithFilters", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findWithFilters(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String customerNo,
            @RequestParam(required = false) String invoiceNo,
            @RequestParam(required = false) String drawerNo,
            @RequestParam(required = false) String cashierUserName,
            @RequestParam(required = false) String routeNo) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "createdDate"));
            return ResponseEntity.ok(salesInvoiceService.findWithFilters(
                startDate, endDate, customerNo, invoiceNo, drawerNo, cashierUserName, routeNo, pageable));
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices with filters: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Invoice update functionality removed - only amendments are allowed

}
