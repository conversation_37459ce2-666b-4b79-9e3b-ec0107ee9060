package lk.sout.business.restaurant.service;

import lk.sout.core.entity.Response;
import lk.sout.business.restaurant.entity.Order;
import lk.sout.business.hr.entity.Employee;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 2/9/2022
 */
public interface OrderService {

    Response save(Order order);

    List<Order> findTop15ByOrderByIdDesc();

    List<Order> findAll();
    
    List<Order> findActive();

    List<Order> findByTableNo(String tableNo);

    Order findByOrderNo(String s);

    Order findById(String id);

    List<Order> findByOrderNoLike(String s);

    List<Order> findByDate(LocalDate date);

    List<Order> findByDateRange(LocalDate startDate, LocalDate endDate);

    List<Order> findByStatus(String status);

    List<Order> findByTableNoAndStatus(String tableNo, String status);

    Response cancelOrder(String orderId);

    boolean deleteRecord(Order order);

    // New filtering methods
    List<Order> findByEmployee(Employee employee);

    List<Order> findByEmployeeId(String employeeId);

    List<Order> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    List<Order> findByOrderNoContaining(String orderNo);

    List<Order> findOrdersByFilters(String tableNo, String orderNo, String employeeId, String status, LocalDateTime startDate, LocalDateTime endDate);
}