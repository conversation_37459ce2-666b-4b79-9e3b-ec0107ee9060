package lk.sout.business.restaurant.service;

import lk.sout.business.restaurant.entity.DeliveryLocation;

import java.util.List;

public interface DeliveryLocationService {

    boolean save(DeliveryLocation deliveryLocation);

    Iterable<DeliveryLocation> findAll(Integer page, Integer pageSize);

    boolean remove(String id);

    List<DeliveryLocation> findByName(String name);

    int getCount();

    List<DeliveryLocation> findAllLocations();

    DeliveryLocation findById(String id);
    
    List<DeliveryLocation> findByLocationNameLike(String name);
}