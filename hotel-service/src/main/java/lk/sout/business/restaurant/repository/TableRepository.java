package lk.sout.business.restaurant.repository;

import lk.sout.business.restaurant.entity.Table;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TableRepository extends MongoRepository<Table, String> {

    List<Table> findAllByTableNoLikeIgnoreCase(String no);

    Table findByTableNo(String tableNo);

    List<Table> findAll();
}