package lk.sout.business.restaurant.repository;

import lk.sout.business.restaurant.entity.Order;
import lk.sout.business.hr.entity.Employee;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OrderRepository extends MongoRepository<Order, String> {

    List<Order> findTop15ByOrderByIdDesc();

    List<Order> findAllByTableNo(String tableNo);

    List<Order> findAllByOrderNoLike(String s);

    Order findByOrderNo(String s);

    List<Order> findByDateBetween(LocalDate s, LocalDate date);

    List<Order> findByActive(boolean active);

    List<Order> findByStatus(String status);

    List<Order> findByTableNoAndStatus(String tableNo, String status);

    // Find orders by employee
    List<Order> findByEmployee(Employee employee);

    List<Order> findByEmployeeId(String employeeId);

    // Find orders by date range
    List<Order> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Find orders by order number like
    List<Order> findByOrderNoContainingIgnoreCase(String orderNo);

    // Complex filtering query
    @Query("{ $and: [ " +
           "{ $or: [ { 'tableNo': { $regex: ?0, $options: 'i' } }, { ?0: null } ] }, " +
           "{ $or: [ { 'orderNo': { $regex: ?1, $options: 'i' } }, { ?1: null } ] }, " +
           "{ $or: [ { 'employee._id': ?2 }, { ?2: null } ] }, " +
           "{ $or: [ { 'status': ?3 }, { ?3: null } ] }, " +
           "{ $or: [ { 'date': { $gte: ?4 } }, { ?4: null } ] }, " +
           "{ $or: [ { 'date': { $lte: ?5 } }, { ?5: null } ] } " +
           "] }")
    List<Order> findOrdersByFilters(String tableNo, String orderNo, String employeeId, String status, LocalDateTime startDate, LocalDateTime endDate);
}