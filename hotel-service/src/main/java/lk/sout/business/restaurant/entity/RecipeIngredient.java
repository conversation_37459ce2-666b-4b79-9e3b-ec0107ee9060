package lk.sout.business.restaurant.entity;

import lk.sout.business.inventory.entity.Item;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.stereotype.Component;

/**
 * Recipe ingredient entity for storing ingredient details in recipes
 */
@Component
public class RecipeIngredient {

    @DBRef
    private Item ingredient; // The ingredient item

    private double quantityInGrams; // Quantity needed in grams

    private String notes; // Optional notes for this ingredient

    public RecipeIngredient() {
    }

    public RecipeIngredient(Item ingredient, double quantityInGrams) {
        this.ingredient = ingredient;
        this.quantityInGrams = quantityInGrams;
    }

    public RecipeIngredient(Item ingredient, double quantityInGrams, String notes) {
        this.ingredient = ingredient;
        this.quantityInGrams = quantityInGrams;
        this.notes = notes;
    }

    public Item getIngredient() {
        return ingredient;
    }

    public void setIngredient(Item ingredient) {
        this.ingredient = ingredient;
    }

    public double getQuantityInGrams() {
        return quantityInGrams;
    }

    public void setQuantityInGrams(double quantityInGrams) {
        this.quantityInGrams = quantityInGrams;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}
