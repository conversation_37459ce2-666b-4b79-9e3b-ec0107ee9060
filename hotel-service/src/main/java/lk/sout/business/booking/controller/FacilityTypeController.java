package lk.sout.business.booking.controller;

import lk.sout.business.booking.entity.FacilityType;
import lk.sout.business.booking.service.FacilityTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/facilityType")
@CrossOrigin
public class FacilityTypeController {

    @Autowired
    private FacilityTypeService facilityTypeService;

    @PostMapping("/save")
    public ResponseEntity<?> save(@RequestBody FacilityType facilityType) {
        try {
            return ResponseEntity.ok(facilityTypeService.save(facilityType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @GetMapping("/findAll")
    public ResponseEntity<?> findAll(@RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            return ResponseEntity.ok(facilityTypeService.findAll(pageable));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @GetMapping("/findAllActive")
    public ResponseEntity<?> findAllActive() {
        try {
            return ResponseEntity.ok(facilityTypeService.findAllActive());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @GetMapping("/findByName")
    public ResponseEntity<?> findByName(@RequestParam String any) {
        try {
            return ResponseEntity.ok(facilityTypeService.findByName(any));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> delete(@PathVariable String id) {
        try {
            facilityTypeService.delete(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
