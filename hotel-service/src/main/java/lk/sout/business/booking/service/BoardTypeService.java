package lk.sout.business.booking.service;

import lk.sout.core.entity.Response;
import lk.sout.business.booking.entity.BoardType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface BoardTypeService {
    Response save(BoardType boardType);

    List<BoardType> findByNameLike(String boardType);

    Page findAllPageable(Pageable pageable);

    BoardType findById(String id);

    Iterable findAll();
}
