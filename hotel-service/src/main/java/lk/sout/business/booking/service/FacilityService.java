package lk.sout.business.booking.service;

import lk.sout.business.booking.entity.Facility;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface FacilityService {

    Facility save(Facility facility);

    Page<Facility> findAll(Pageable pageable);

    List<Facility> findByName(String name);

    List<Facility> findByFacilityName(String name);

    List<Facility> findByFacilityNo(String facilityNo);

    List<Facility> findAllActive();

    void delete(String id);
}
