package lk.sout.business.booking.repository;

import lk.sout.business.booking.entity.FacilityType;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FacilityTypeRepository extends MongoRepository<FacilityType, String> {
    List<FacilityType> findByNameLikeIgnoreCase(String name);
    
    List<FacilityType> findAllByActive(boolean active);
    
    FacilityType findByName(String name);
}
