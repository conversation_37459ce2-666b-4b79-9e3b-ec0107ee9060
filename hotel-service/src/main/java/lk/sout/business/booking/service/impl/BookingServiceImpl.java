package lk.sout.business.booking.service.impl;

import lk.sout.business.booking.entity.Booking;
import lk.sout.business.inventory.entity.Item;
import lk.sout.business.inventory.service.ItemService;
import lk.sout.business.trade.service.CashDrawerService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.entity.User;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.UserService;
import lk.sout.business.inventory.entity.Stock;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.booking.entity.BoardType;
import lk.sout.business.booking.repository.BookingRepository;
import lk.sout.business.booking.repository.FacilityRepository;
import lk.sout.business.booking.service.BoardTypeService;
import lk.sout.business.booking.service.BookingService;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class BookingServiceImpl implements BookingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BookingServiceImpl.class);

    @Autowired
    BookingRepository bookingRepository;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    UserService userService;

    @Autowired
    StockService stockService;

    @Autowired
    CashDrawerService cashDrawerService;

    @Autowired
    FacilityRepository facilityRepository;

    @Autowired
    ItemService itemService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    BoardTypeService boardTypeService;

    @Autowired
    Response response;

    @Override
    public Response saveBooking(Booking booking) {
        try {
            String seqId = "";
            Sequence sequence = sequenceService.findSequenceByName("Booking Code");
            seqId = (sequence.getPrefix() + String.valueOf(sequence.getCounter() + 1));
            booking.setBookingCode(seqId);
            sequence = null;

            BoardType boardtype = boardTypeService.findById(booking.getBoardType().getId());
            int dates = booking.getFrom().compareTo(booking.getTo());
            booking.setRoomCharge(boardtype.getCharge() * (dates * -1));
            booking.setTodayBook(true);
            booking.setBookingStatus(metaDataService.searchMetaData("Booked", "BookingStatus"));
            sequenceService.incrementSequence("Booking Code");
            bookingRepository.save(booking);
            response.setCode(200);
            response.setMessage("Booking Created Successfully");
        } catch (Exception e) {
            LOGGER.error("Booking Category Failed", e.getMessage());
            response.setCode(501);
            response.setMessage("Booking Creating Failed");
            response.setData(e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional
    public Response saveInvoice(Booking bookingInvoice) {
        try {
            User user = userService.findUser();

            Booking booking = bookingRepository.findByIdAndBookingCode(bookingInvoice.getId(),
                    bookingInvoice.getBookingCode());

            String seqId = "";
            Sequence sequence = sequenceService.findSequenceByName("Booking Invoice");
            seqId = (sequence.getPrefix() + String.valueOf(sequence.getCounter() + 1));
            booking.setInvoiceNo(seqId);
            sequence = null;

            booking.setInvoiceDate(LocalDateTime.now());

            if (bookingInvoice.getTotalAmount().compareTo(bookingInvoice.getPayment()) <= 0) {
                bookingInvoice.setInvoiceStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
            } else if (bookingInvoice.getPayment() == 0) {
                bookingInvoice.setInvoiceStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
            } else if (bookingInvoice.getTotalAmount().compareTo(bookingInvoice.getPayment()) > 0 &&
                    bookingInvoice.getPayment() > 0) {
                bookingInvoice.setInvoiceStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            booking.setDueDate(bookingInvoice.getDueDate());
            booking.setPayment(bookingInvoice.getPayment());
            booking.setPrice(bookingInvoice.getPrice());
            booking.setServiceCharge(bookingInvoice.getServiceCharge());
            booking.setSubTotal(bookingInvoice.getSubTotal());
            booking.setTotalAmount(bookingInvoice.getTotalAmount());
            booking.setTotalDiscount(bookingInvoice.getTotalDiscount());
            booking.setSalesInvoiceRecords(bookingInvoice.getSalesInvoiceRecords());

            for (SalesInvoiceRecord salesInvoiceRecord : booking.getSalesInvoiceRecords()) {
                salesInvoiceRecord.setDate(LocalDate.now());
                Item item = itemService.findOne(salesInvoiceRecord.getItemCode());
                if (item.isManageStock()) {
                    //TODO ADD STOCK CODE TO SALES INVOICE RECORD
                    Stock stock = stockService.findByItemCodeAndWarehouseAndPrice(item.getItemCode(), user.getWarehouseCode(), item.getSellingPrice());
                    if (item.isManageStock()) {
                        stock.setQuantity(stock.getQuantity() - salesInvoiceRecord.getQuantity());
                        stockService.save(stock,"Booking Item Deduction",item.getItemCode());
                    }
                }
            }

            booking.setCounterNo(user.getCashDrawerNo());

            sequenceService.incrementSequence("Booking Invoice");
            booking.setBookingStatus(metaDataService.searchMetaData("Checked", "BookingStatus"));
            Booking resSI = bookingRepository.save(booking);
            response.setCode(200);
            response.setData(booking.getInvoiceNo());
            response.setMessage("Booking Invoice Successfully saved");
            return response;
        } catch (Exception e) {
            LOGGER.error("Booking Invoice Saving Failed" + e.getMessage());
            response.setCode(501);
            response.setMessage("Booking Invoice Saving Failed");
            response.setData(e.getMessage());
            return response;
        }
    }

    @Override
    public List<Booking> findAllPendingBookings() {
        try {
            MetaData booked = metaDataService.searchMetaData("Booked", "BookingStatus");
            List<Booking> bookings = bookingRepository.findAllByBookingStatusId(booked.getId());
            return bookings;
        } catch (Exception e) {
            LOGGER.error("Booking List Retrieving Failed");
            return null;
        }
    }

    @Override
    public Iterable<Booking> findAll(Pageable pageable) {
        try {
            Iterable<Booking> r = bookingRepository.findAll(pageable);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Find All Booking Failed", ex.getMessage());
            return null;
        }
    }

    @Override
    public Booking findOne(String id) {
        try {
            Optional<Booking> bookings = bookingRepository.findById(id);
            return bookings.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Booking failed : " + ex.getMessage());
            return null;
        }
    }


    @Override
    public Integer getAllCount() {
        try {
            Integer count = Math.toIntExact(bookingRepository.count());
            return count;
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Item failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean checkFromDate(String roomId, LocalDate from) {
        try {
            Booking r = bookingRepository.findByFacilityAndFrom(roomId, from);
            if (null != r.getFacility().getId()) {
                if (null != r.getFrom()) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } catch (Exception ex) {
            LOGGER.error("Check From Date Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Booking> findAllBookedRoom(LocalDate today) {
        try {
            List<Booking> bookings = bookingRepository.findAllByFrom(today);
            return bookings;
        } catch (Exception ex) {
            LOGGER.error("Check From Date Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Booking findById(String id) {
        try {
            Booking r = bookingRepository.findAllById(id);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Booking failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Booking> findAllByStatusLikeIgnoreCase(String booked) {
        try {
            List<Booking> r = bookingRepository.findAllByBookingStatusLikeIgnoreCase(booked);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Booking failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Booking> findBookingByRoomNo(String id, String status) {
        try {
            List<Booking> res = bookingRepository.findAllByFacilityAndBookingStatus(id, status);
            return res;
        } catch (Exception ex) {
            LOGGER.error("Returning Room No Failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Booking findByIdAndRoomId(String bookingId, String roomId) {
        try {
            Booking r = bookingRepository.findByIdAndFacility(bookingId, roomId);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Returning booking failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Booking findBookingInvoiceByInvNo(String invoiceNo) {
        try {
            Booking booking = bookingRepository.findByInvoiceNo(invoiceNo);
            return booking;
        } catch (Exception e) {
            LOGGER.error(e + "Booking Retrieving failed");
            return null;
        }
    }

}
