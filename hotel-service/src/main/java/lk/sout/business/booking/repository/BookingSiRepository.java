package lk.sout.business.booking.repository;

import lk.sout.business.booking.entity.BookingSi;
import lk.sout.business.trade.entity.Customer;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BookingSiRepository extends MongoRepository<BookingSi, String> {

    BookingSi findByInvoiceNo(String invoiceNo);

    List<BookingSi> findAllByCustomer(Customer customer);

    List<BookingSi> findByInvoiceNoLike(String keyInvoiceNo);

    List<BookingSi> findByDateBetween(LocalDateTime date, LocalDateTime eDate);
}
