package lk.sout.business.booking.service;

import lk.sout.business.booking.entity.Booking;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;


public interface BookingService {

    Response saveBooking(Booking booking);

    Response saveInvoice(Booking bookingInvoice);

    List<Booking> findAllPendingBookings();

    Iterable<Booking> findAll(Pageable pageable);

    Integer getAllCount();

    boolean checkFromDate(String roomId, LocalDate from);

    List<Booking> findAllBookedRoom(LocalDate today);

    Booking findById(String id);

    Booking findOne(String id);

    List<Booking> findAllByStatusLikeIgnoreCase(String booked);

    List<Booking> findBookingByRoomNo(String id, String status);

    Booking findByIdAndRoomId(String bookingId, String roomId);

    Booking findBookingInvoiceByInvNo(String invoiceNo);
}
