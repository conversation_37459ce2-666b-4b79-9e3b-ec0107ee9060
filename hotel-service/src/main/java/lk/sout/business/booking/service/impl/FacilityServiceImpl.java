package lk.sout.business.booking.service.impl;

import lk.sout.business.booking.entity.Facility;
import lk.sout.business.booking.repository.FacilityRepository;
import lk.sout.business.booking.service.FacilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FacilityServiceImpl implements FacilityService {

    @Autowired
    private FacilityRepository facilityRepository;

    @Override
    public Facility save(Facility facility) {
        return facilityRepository.save(facility);
    }

    @Override
    public Page<Facility> findAll(Pageable pageable) {
        return facilityRepository.findAll(pageable);
    }

    @Override
    public List<Facility> findByName(String name) {
        return facilityRepository.findByFacilityNameLikeIgnoreCase("%" + name + "%");
    }

    @Override
    public List<Facility> findByFacilityName(String name) {
        return facilityRepository.findByFacilityNameLikeIgnoreCase("%" + name + "%");
    }

    @Override
    public List<Facility> findByFacilityNo(String facilityNo) {
        return facilityRepository.findAllByFacilityNoLikeIgnoreCase("%" + facilityNo + "%");
    }

    @Override
    public List<Facility> findAllActive() {
        return facilityRepository.findAllByActive(true);
    }

    @Override
    public void delete(String id) {
        facilityRepository.deleteById(id);
    }
}
